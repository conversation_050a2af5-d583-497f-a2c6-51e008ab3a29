/**
 * Format currency in Egyptian Pounds
 */
export declare const formatCurrency: (amount: number, currency?: string) => string;
/**
 * Format date in Arabic locale
 */
export declare const formatDate: (date: string | Date, options?: Intl.DateTimeFormatOptions) => string;
/**
 * Format relative time in Arabic
 */
export declare const formatRelativeTime: (date: string | Date) => string;
/**
 * Validate Egyptian phone number
 */
export declare const validateEgyptianPhone: (phone: string) => boolean;
/**
 * Generate initials from name
 */
export declare const getInitials: (name: string) => string;
/**
 * Calculate progress percentage
 */
export declare const calculateProgress: (completed: number, total: number) => number;
/**
 * Get status color based on status value
 */
export declare const getStatusColor: (status: string) => string;
/**
 * Debounce function
 */
export declare const debounce: <T extends (...args: any[]) => any>(func: T, wait: number) => ((...args: Parameters<T>) => void);
/**
 * Generate random ID
 */
export declare const generateId: () => string;
/**
 * Truncate text with ellipsis
 */
export declare const truncateText: (text: string, maxLength: number) => string;
