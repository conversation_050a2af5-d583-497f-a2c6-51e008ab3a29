import { z } from 'zod';
export declare enum TaskPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
export declare enum TaskStatus {
    TODO = "todo",
    IN_PROGRESS = "in_progress",
    REVIEW = "review",
    TESTING = "testing",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}
export declare enum TaskCategory {
    LIGHT = "light",// 1-4 hours
    MEDIUM = "medium",// 4-8 hours
    EXTREME = "extreme"
}
export declare const TaskSchema: z.ZodObject<{
    id: z.ZodString;
    title: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    category: z.ZodNativeEnum<typeof TaskCategory>;
    priority: z.ZodNativeEnum<typeof TaskPriority>;
    status: z.ZodNativeEnum<typeof TaskStatus>;
    projectId: z.ZodOptional<z.ZodString>;
    assignedTo: z.<PERSON>od<PERSON>y<z.ZodString, "many">;
    createdBy: z.ZodString;
    estimatedHours: z.ZodNumber;
    actualHours: z.ZodOptional<z.ZodNumber>;
    dueDate: z.ZodOptional<z.ZodString>;
    dependencies: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    attachments: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
    completedAt: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    status: TaskStatus;
    createdAt: string;
    updatedAt: string;
    priority: TaskPriority;
    title: string;
    category: TaskCategory;
    assignedTo: string[];
    createdBy: string;
    estimatedHours: number;
    description?: string | undefined;
    projectId?: string | undefined;
    actualHours?: number | undefined;
    dueDate?: string | undefined;
    dependencies?: string[] | undefined;
    tags?: string[] | undefined;
    attachments?: string[] | undefined;
    completedAt?: string | undefined;
}, {
    id: string;
    status: TaskStatus;
    createdAt: string;
    updatedAt: string;
    priority: TaskPriority;
    title: string;
    category: TaskCategory;
    assignedTo: string[];
    createdBy: string;
    estimatedHours: number;
    description?: string | undefined;
    projectId?: string | undefined;
    actualHours?: number | undefined;
    dueDate?: string | undefined;
    dependencies?: string[] | undefined;
    tags?: string[] | undefined;
    attachments?: string[] | undefined;
    completedAt?: string | undefined;
}>;
export type Task = z.infer<typeof TaskSchema>;
export declare const CreateTaskSchema: z.ZodObject<Omit<{
    id: z.ZodString;
    title: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    category: z.ZodNativeEnum<typeof TaskCategory>;
    priority: z.ZodNativeEnum<typeof TaskPriority>;
    status: z.ZodNativeEnum<typeof TaskStatus>;
    projectId: z.ZodOptional<z.ZodString>;
    assignedTo: z.ZodArray<z.ZodString, "many">;
    createdBy: z.ZodString;
    estimatedHours: z.ZodNumber;
    actualHours: z.ZodOptional<z.ZodNumber>;
    dueDate: z.ZodOptional<z.ZodString>;
    dependencies: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    attachments: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
    completedAt: z.ZodOptional<z.ZodString>;
}, "id" | "createdAt" | "updatedAt" | "actualHours" | "completedAt">, "strip", z.ZodTypeAny, {
    status: TaskStatus;
    priority: TaskPriority;
    title: string;
    category: TaskCategory;
    assignedTo: string[];
    createdBy: string;
    estimatedHours: number;
    description?: string | undefined;
    projectId?: string | undefined;
    dueDate?: string | undefined;
    dependencies?: string[] | undefined;
    tags?: string[] | undefined;
    attachments?: string[] | undefined;
}, {
    status: TaskStatus;
    priority: TaskPriority;
    title: string;
    category: TaskCategory;
    assignedTo: string[];
    createdBy: string;
    estimatedHours: number;
    description?: string | undefined;
    projectId?: string | undefined;
    dueDate?: string | undefined;
    dependencies?: string[] | undefined;
    tags?: string[] | undefined;
    attachments?: string[] | undefined;
}>;
export type CreateTaskData = z.infer<typeof CreateTaskSchema>;
export declare const UpdateTaskSchema: z.ZodObject<{
    status: z.ZodOptional<z.ZodNativeEnum<typeof TaskStatus>>;
    description: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    priority: z.ZodOptional<z.ZodNativeEnum<typeof TaskPriority>>;
    title: z.ZodOptional<z.ZodString>;
    category: z.ZodOptional<z.ZodNativeEnum<typeof TaskCategory>>;
    projectId: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    assignedTo: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    createdBy: z.ZodOptional<z.ZodString>;
    estimatedHours: z.ZodOptional<z.ZodNumber>;
    dueDate: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    dependencies: z.ZodOptional<z.ZodOptional<z.ZodArray<z.ZodString, "many">>>;
    tags: z.ZodOptional<z.ZodOptional<z.ZodArray<z.ZodString, "many">>>;
    attachments: z.ZodOptional<z.ZodOptional<z.ZodArray<z.ZodString, "many">>>;
}, "strip", z.ZodTypeAny, {
    status?: TaskStatus | undefined;
    description?: string | undefined;
    priority?: TaskPriority | undefined;
    title?: string | undefined;
    category?: TaskCategory | undefined;
    projectId?: string | undefined;
    assignedTo?: string[] | undefined;
    createdBy?: string | undefined;
    estimatedHours?: number | undefined;
    dueDate?: string | undefined;
    dependencies?: string[] | undefined;
    tags?: string[] | undefined;
    attachments?: string[] | undefined;
}, {
    status?: TaskStatus | undefined;
    description?: string | undefined;
    priority?: TaskPriority | undefined;
    title?: string | undefined;
    category?: TaskCategory | undefined;
    projectId?: string | undefined;
    assignedTo?: string[] | undefined;
    createdBy?: string | undefined;
    estimatedHours?: number | undefined;
    dueDate?: string | undefined;
    dependencies?: string[] | undefined;
    tags?: string[] | undefined;
    attachments?: string[] | undefined;
}>;
export type UpdateTaskData = z.infer<typeof UpdateTaskSchema>;
export declare const TaskTimeLogSchema: z.ZodObject<{
    id: z.ZodString;
    taskId: z.ZodString;
    userId: z.ZodString;
    hours: z.ZodNumber;
    description: z.ZodOptional<z.ZodString>;
    date: z.ZodString;
    createdAt: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    date: string;
    createdAt: string;
    userId: string;
    taskId: string;
    hours: number;
    description?: string | undefined;
}, {
    id: string;
    date: string;
    createdAt: string;
    userId: string;
    taskId: string;
    hours: number;
    description?: string | undefined;
}>;
export type TaskTimeLog = z.infer<typeof TaskTimeLogSchema>;
export declare const TaskCommentSchema: z.ZodObject<{
    id: z.ZodString;
    taskId: z.ZodString;
    userId: z.ZodString;
    content: z.ZodString;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: string;
    updatedAt: string;
    content: string;
    userId: string;
    taskId: string;
}, {
    id: string;
    createdAt: string;
    updatedAt: string;
    content: string;
    userId: string;
    taskId: string;
}>;
export type TaskComment = z.infer<typeof TaskCommentSchema>;
