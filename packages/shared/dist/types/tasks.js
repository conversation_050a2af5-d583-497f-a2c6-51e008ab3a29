"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskCommentSchema = exports.TaskTimeLogSchema = exports.UpdateTaskSchema = exports.CreateTaskSchema = exports.TaskSchema = exports.TaskCategory = exports.TaskStatus = exports.TaskPriority = void 0;
const zod_1 = require("zod");
// Task Priority
var TaskPriority;
(function (TaskPriority) {
    TaskPriority["LOW"] = "low";
    TaskPriority["MEDIUM"] = "medium";
    TaskPriority["HIGH"] = "high";
    TaskPriority["URGENT"] = "urgent";
})(TaskPriority || (exports.TaskPriority = TaskPriority = {}));
// Task Status
var TaskStatus;
(function (TaskStatus) {
    TaskStatus["TODO"] = "todo";
    TaskStatus["IN_PROGRESS"] = "in_progress";
    TaskStatus["REVIEW"] = "review";
    TaskStatus["TESTING"] = "testing";
    TaskStatus["COMPLETED"] = "completed";
    TaskStatus["CANCELLED"] = "cancelled";
})(TaskStatus || (exports.TaskStatus = TaskStatus = {}));
// Task Category (with time estimates in hours)
var TaskCategory;
(function (TaskCategory) {
    TaskCategory["LIGHT"] = "light";
    TaskCategory["MEDIUM"] = "medium";
    TaskCategory["EXTREME"] = "extreme";
})(TaskCategory || (exports.TaskCategory = TaskCategory = {}));
// Task Schema
exports.TaskSchema = zod_1.z.object({
    id: zod_1.z.string(),
    title: zod_1.z.string(),
    description: zod_1.z.string().optional(),
    category: zod_1.z.nativeEnum(TaskCategory),
    priority: zod_1.z.nativeEnum(TaskPriority),
    status: zod_1.z.nativeEnum(TaskStatus),
    projectId: zod_1.z.string().optional(),
    assignedTo: zod_1.z.array(zod_1.z.string()),
    createdBy: zod_1.z.string(),
    estimatedHours: zod_1.z.number().positive(),
    actualHours: zod_1.z.number().optional(),
    dueDate: zod_1.z.string().optional(),
    dependencies: zod_1.z.array(zod_1.z.string()).optional(),
    tags: zod_1.z.array(zod_1.z.string()).optional(),
    attachments: zod_1.z.array(zod_1.z.string()).optional(),
    createdAt: zod_1.z.string(),
    updatedAt: zod_1.z.string(),
    completedAt: zod_1.z.string().optional(),
});
// Create Task Schema
exports.CreateTaskSchema = exports.TaskSchema.omit({
    id: true,
    createdAt: true,
    updatedAt: true,
    completedAt: true,
    actualHours: true,
});
// Update Task Schema
exports.UpdateTaskSchema = exports.CreateTaskSchema.partial();
// Task Time Log Schema
exports.TaskTimeLogSchema = zod_1.z.object({
    id: zod_1.z.string(),
    taskId: zod_1.z.string(),
    userId: zod_1.z.string(),
    hours: zod_1.z.number().positive(),
    description: zod_1.z.string().optional(),
    date: zod_1.z.string(),
    createdAt: zod_1.z.string(),
});
// Task Comment Schema
exports.TaskCommentSchema = zod_1.z.object({
    id: zod_1.z.string(),
    taskId: zod_1.z.string(),
    userId: zod_1.z.string(),
    content: zod_1.z.string(),
    createdAt: zod_1.z.string(),
    updatedAt: zod_1.z.string(),
});
