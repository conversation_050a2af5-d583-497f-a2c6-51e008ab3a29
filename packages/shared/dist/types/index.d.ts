export * from './auth';
export * from './projects';
export * from './clients';
export * from './tasks';
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    errors?: Record<string, string[]>;
}
export interface PaginatedResponse<T = any> {
    success: boolean;
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
export interface BaseFilter {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export interface DateRangeFilter {
    startDate?: string;
    endDate?: string;
}
export interface Money {
    amount: number;
    currency: 'EGP' | 'USD' | 'EUR';
}
export interface FileUpload {
    id: string;
    name: string;
    size: number;
    type: string;
    url: string;
    uploadedBy: string;
    uploadedAt: string;
}
