import { z } from 'zod';
export declare enum UserRole {
    ADMIN = "admin",
    SALES_MANAGER = "sales_manager",
    MEDIA_BUYER = "media_buyer",
    DEVELOPER = "developer",
    DESIGNER = "designer",
    WORDPRESS_DEVELOPER = "wordpress_developer"
}
export declare enum UserStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SUSPENDED = "suspended"
}
export declare const UserSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    firstName: z.ZodString;
    lastName: z.ZodString;
    role: z.ZodNativeEnum<typeof UserRole>;
    status: z.ZodNativeEnum<typeof UserStatus>;
    avatar: z.ZodOptional<z.ZodString>;
    phone: z.ZodOptional<z.ZodString>;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
    lastLoginAt: z.ZodOptional<z.ZodString>;
}, "strip", z.<PERSON><PERSON><PERSON><PERSON>Any, {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    createdAt: string;
    updatedAt: string;
    avatar?: string | undefined;
    phone?: string | undefined;
    lastLoginAt?: string | undefined;
}, {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    createdAt: string;
    updatedAt: string;
    avatar?: string | undefined;
    phone?: string | undefined;
    lastLoginAt?: string | undefined;
}>;
export type User = z.infer<typeof UserSchema>;
export declare const LoginSchema: z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    password: string;
}, {
    email: string;
    password: string;
}>;
export declare const RegisterSchema: z.ZodEffects<z.ZodObject<{
    email: z.ZodString;
    password: z.ZodString;
    confirmPassword: z.ZodString;
    firstName: z.ZodString;
    lastName: z.ZodString;
    role: z.ZodNativeEnum<typeof UserRole>;
}, "strip", z.ZodTypeAny, {
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    password: string;
    confirmPassword: string;
}, {
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    password: string;
    confirmPassword: string;
}>, {
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    password: string;
    confirmPassword: string;
}, {
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    password: string;
    confirmPassword: string;
}>;
export type LoginData = z.infer<typeof LoginSchema>;
export type RegisterData = z.infer<typeof RegisterSchema>;
export declare const TokenResponseSchema: z.ZodObject<{
    access: z.ZodString;
    refresh: z.ZodString;
    user: z.ZodObject<{
        id: z.ZodString;
        email: z.ZodString;
        firstName: z.ZodString;
        lastName: z.ZodString;
        role: z.ZodNativeEnum<typeof UserRole>;
        status: z.ZodNativeEnum<typeof UserStatus>;
        avatar: z.ZodOptional<z.ZodString>;
        phone: z.ZodOptional<z.ZodString>;
        createdAt: z.ZodString;
        updatedAt: z.ZodString;
        lastLoginAt: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        role: UserRole;
        status: UserStatus;
        createdAt: string;
        updatedAt: string;
        avatar?: string | undefined;
        phone?: string | undefined;
        lastLoginAt?: string | undefined;
    }, {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        role: UserRole;
        status: UserStatus;
        createdAt: string;
        updatedAt: string;
        avatar?: string | undefined;
        phone?: string | undefined;
        lastLoginAt?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    access: string;
    refresh: string;
    user: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        role: UserRole;
        status: UserStatus;
        createdAt: string;
        updatedAt: string;
        avatar?: string | undefined;
        phone?: string | undefined;
        lastLoginAt?: string | undefined;
    };
}, {
    access: string;
    refresh: string;
    user: {
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        role: UserRole;
        status: UserStatus;
        createdAt: string;
        updatedAt: string;
        avatar?: string | undefined;
        phone?: string | undefined;
        lastLoginAt?: string | undefined;
    };
}>;
export type TokenResponse = z.infer<typeof TokenResponseSchema>;
