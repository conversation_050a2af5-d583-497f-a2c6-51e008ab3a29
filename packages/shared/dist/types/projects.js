"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateProjectSchema = exports.CreateProjectSchema = exports.ProjectSchema = exports.ProjectType = exports.ProjectPriority = exports.ProjectStatus = void 0;
const zod_1 = require("zod");
// Project Status
var ProjectStatus;
(function (ProjectStatus) {
    ProjectStatus["PLANNING"] = "planning";
    ProjectStatus["DEVELOPMENT"] = "development";
    ProjectStatus["TESTING"] = "testing";
    ProjectStatus["DEPLOYMENT"] = "deployment";
    ProjectStatus["MAINTENANCE"] = "maintenance";
    ProjectStatus["COMPLETED"] = "completed";
    ProjectStatus["CANCELLED"] = "cancelled";
})(ProjectStatus || (exports.ProjectStatus = ProjectStatus = {}));
// Project Priority
var ProjectPriority;
(function (ProjectPriority) {
    ProjectPriority["LOW"] = "low";
    ProjectPriority["MEDIUM"] = "medium";
    ProjectPriority["HIGH"] = "high";
    ProjectPriority["URGENT"] = "urgent";
})(ProjectPriority || (exports.ProjectPriority = ProjectPriority = {}));
// Project Type
var ProjectType;
(function (ProjectType) {
    ProjectType["WEBSITE"] = "website";
    ProjectType["MOBILE_APP"] = "mobile_app";
    ProjectType["WEB_APP"] = "web_app";
    ProjectType["ECOMMERCE"] = "ecommerce";
    ProjectType["WORDPRESS"] = "wordpress";
    ProjectType["MAINTENANCE"] = "maintenance";
    ProjectType["MARKETING"] = "marketing";
})(ProjectType || (exports.ProjectType = ProjectType = {}));
// Project Schema
exports.ProjectSchema = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string(),
    description: zod_1.z.string().optional(),
    type: zod_1.z.nativeEnum(ProjectType),
    status: zod_1.z.nativeEnum(ProjectStatus),
    priority: zod_1.z.nativeEnum(ProjectPriority),
    clientId: zod_1.z.string(),
    assignedTeam: zod_1.z.array(zod_1.z.string()),
    startDate: zod_1.z.string(),
    endDate: zod_1.z.string().optional(),
    budget: zod_1.z.number().optional(),
    actualCost: zod_1.z.number().optional(),
    progress: zod_1.z.number().min(0).max(100),
    domains: zod_1.z.array(zod_1.z.string()).optional(),
    serverCredentials: zod_1.z.object({
        host: zod_1.z.string(),
        username: zod_1.z.string(),
        password: zod_1.z.string(),
    }).optional(),
    createdAt: zod_1.z.string(),
    updatedAt: zod_1.z.string(),
});
// Create Project Schema
exports.CreateProjectSchema = exports.ProjectSchema.omit({
    id: true,
    createdAt: true,
    updatedAt: true,
    progress: true,
    actualCost: true,
});
// Update Project Schema
exports.UpdateProjectSchema = exports.CreateProjectSchema.partial();
