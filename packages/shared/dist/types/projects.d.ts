import { z } from 'zod';
export declare enum ProjectStatus {
    PLANNING = "planning",
    DEVELOPMENT = "development",
    TESTING = "testing",
    DEPLOYMENT = "deployment",
    MAINTENANCE = "maintenance",
    COMPLETED = "completed",
    CANCELLED = "cancelled"
}
export declare enum ProjectPriority {
    LOW = "low",
    MEDIUM = "medium",
    HIGH = "high",
    URGENT = "urgent"
}
export declare enum ProjectType {
    WEBSITE = "website",
    MOBILE_APP = "mobile_app",
    WEB_APP = "web_app",
    ECOMMERCE = "ecommerce",
    WORDPRESS = "wordpress",
    MAINTENANCE = "maintenance",
    MARKETING = "marketing"
}
export declare const ProjectSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    type: z.ZodNativeEnum<typeof ProjectType>;
    status: z.ZodNativeEnum<typeof ProjectStatus>;
    priority: z.ZodNativeEnum<typeof ProjectPriority>;
    clientId: z.ZodString;
    assignedTeam: z.<PERSON><z.ZodString, "many">;
    startDate: z.ZodString;
    endDate: z.ZodOptional<z.ZodString>;
    budget: z.ZodOptional<z.ZodNumber>;
    actualCost: z.ZodOptional<z.ZodNumber>;
    progress: z.ZodNumber;
    domains: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    serverCredentials: z.ZodOptional<z.ZodObject<{
        host: z.ZodString;
        username: z.ZodString;
        password: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        password: string;
        host: string;
        username: string;
    }, {
        password: string;
        host: string;
        username: string;
    }>>;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    status: ProjectStatus;
    type: ProjectType;
    createdAt: string;
    updatedAt: string;
    name: string;
    priority: ProjectPriority;
    clientId: string;
    assignedTeam: string[];
    startDate: string;
    progress: number;
    description?: string | undefined;
    endDate?: string | undefined;
    budget?: number | undefined;
    actualCost?: number | undefined;
    domains?: string[] | undefined;
    serverCredentials?: {
        password: string;
        host: string;
        username: string;
    } | undefined;
}, {
    id: string;
    status: ProjectStatus;
    type: ProjectType;
    createdAt: string;
    updatedAt: string;
    name: string;
    priority: ProjectPriority;
    clientId: string;
    assignedTeam: string[];
    startDate: string;
    progress: number;
    description?: string | undefined;
    endDate?: string | undefined;
    budget?: number | undefined;
    actualCost?: number | undefined;
    domains?: string[] | undefined;
    serverCredentials?: {
        password: string;
        host: string;
        username: string;
    } | undefined;
}>;
export type Project = z.infer<typeof ProjectSchema>;
export declare const CreateProjectSchema: z.ZodObject<Omit<{
    id: z.ZodString;
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    type: z.ZodNativeEnum<typeof ProjectType>;
    status: z.ZodNativeEnum<typeof ProjectStatus>;
    priority: z.ZodNativeEnum<typeof ProjectPriority>;
    clientId: z.ZodString;
    assignedTeam: z.ZodArray<z.ZodString, "many">;
    startDate: z.ZodString;
    endDate: z.ZodOptional<z.ZodString>;
    budget: z.ZodOptional<z.ZodNumber>;
    actualCost: z.ZodOptional<z.ZodNumber>;
    progress: z.ZodNumber;
    domains: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    serverCredentials: z.ZodOptional<z.ZodObject<{
        host: z.ZodString;
        username: z.ZodString;
        password: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        password: string;
        host: string;
        username: string;
    }, {
        password: string;
        host: string;
        username: string;
    }>>;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
}, "id" | "createdAt" | "updatedAt" | "actualCost" | "progress">, "strip", z.ZodTypeAny, {
    status: ProjectStatus;
    type: ProjectType;
    name: string;
    priority: ProjectPriority;
    clientId: string;
    assignedTeam: string[];
    startDate: string;
    description?: string | undefined;
    endDate?: string | undefined;
    budget?: number | undefined;
    domains?: string[] | undefined;
    serverCredentials?: {
        password: string;
        host: string;
        username: string;
    } | undefined;
}, {
    status: ProjectStatus;
    type: ProjectType;
    name: string;
    priority: ProjectPriority;
    clientId: string;
    assignedTeam: string[];
    startDate: string;
    description?: string | undefined;
    endDate?: string | undefined;
    budget?: number | undefined;
    domains?: string[] | undefined;
    serverCredentials?: {
        password: string;
        host: string;
        username: string;
    } | undefined;
}>;
export type CreateProjectData = z.infer<typeof CreateProjectSchema>;
export declare const UpdateProjectSchema: z.ZodObject<{
    status: z.ZodOptional<z.ZodNativeEnum<typeof ProjectStatus>>;
    type: z.ZodOptional<z.ZodNativeEnum<typeof ProjectType>>;
    name: z.ZodOptional<z.ZodString>;
    description: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    priority: z.ZodOptional<z.ZodNativeEnum<typeof ProjectPriority>>;
    clientId: z.ZodOptional<z.ZodString>;
    assignedTeam: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    startDate: z.ZodOptional<z.ZodString>;
    endDate: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    budget: z.ZodOptional<z.ZodOptional<z.ZodNumber>>;
    domains: z.ZodOptional<z.ZodOptional<z.ZodArray<z.ZodString, "many">>>;
    serverCredentials: z.ZodOptional<z.ZodOptional<z.ZodObject<{
        host: z.ZodString;
        username: z.ZodString;
        password: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        password: string;
        host: string;
        username: string;
    }, {
        password: string;
        host: string;
        username: string;
    }>>>;
}, "strip", z.ZodTypeAny, {
    status?: ProjectStatus | undefined;
    type?: ProjectType | undefined;
    name?: string | undefined;
    description?: string | undefined;
    priority?: ProjectPriority | undefined;
    clientId?: string | undefined;
    assignedTeam?: string[] | undefined;
    startDate?: string | undefined;
    endDate?: string | undefined;
    budget?: number | undefined;
    domains?: string[] | undefined;
    serverCredentials?: {
        password: string;
        host: string;
        username: string;
    } | undefined;
}, {
    status?: ProjectStatus | undefined;
    type?: ProjectType | undefined;
    name?: string | undefined;
    description?: string | undefined;
    priority?: ProjectPriority | undefined;
    clientId?: string | undefined;
    assignedTeam?: string[] | undefined;
    startDate?: string | undefined;
    endDate?: string | undefined;
    budget?: number | undefined;
    domains?: string[] | undefined;
    serverCredentials?: {
        password: string;
        host: string;
        username: string;
    } | undefined;
}>;
export type UpdateProjectData = z.infer<typeof UpdateProjectSchema>;
