import { z } from 'zod';
export declare enum ClientMood {
    HAPPY = "happy",
    NEUTRAL = "neutral",
    CONCERNED = "concerned",
    ANGRY = "angry"
}
export declare enum EgyptianGovernorate {
    CAIRO = "cairo",
    ALEXANDRIA = "alexandria",
    GIZA = "giza",
    QALYUBIA = "qalyubia",
    PORT_SAID = "port_said",
    SUEZ = "suez",
    LUXOR = "luxor",
    ASWAN = "aswan",
    ASYUT = "asyut",
    BEHEIRA = "beheira",
    BENI_SUEF = "beni_suef",
    DAKAHLIA = "dakahlia",
    DAMIETTA = "damietta",
    FAYYUM = "fayyum",
    GHARBIA = "gharbia",
    ISMAILIA = "ismailia",
    KAFR_EL_SHEIKH = "kafr_el_sheikh",
    MATROUH = "matrouh",
    MINYA = "minya",
    MONUFIA = "monufia",
    NEW_VALLEY = "new_valley",
    NORTH_SINAI = "north_sinai",
    QENA = "qena",
    RED_SEA = "red_sea",
    SHARQIA = "sharqia",
    SOHAG = "sohag",
    SOUTH_SINAI = "south_sinai"
}
export declare const ClientSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    email: z.ZodString;
    phone: z.ZodString;
    company: z.ZodOptional<z.ZodString>;
    website: z.ZodOptional<z.ZodString>;
    address: z.ZodOptional<z.ZodString>;
    governorate: z.ZodOptional<z.ZodNativeEnum<typeof EgyptianGovernorate>>;
    mood: z.ZodNativeEnum<typeof ClientMood>;
    salesRepId: z.ZodString;
    notes: z.ZodOptional<z.ZodString>;
    totalProjects: z.ZodDefault<z.ZodNumber>;
    totalRevenue: z.ZodDefault<z.ZodNumber>;
    lastContactDate: z.ZodOptional<z.ZodString>;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    phone: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    mood: ClientMood;
    salesRepId: string;
    totalProjects: number;
    totalRevenue: number;
    website?: string | undefined;
    company?: string | undefined;
    address?: string | undefined;
    governorate?: EgyptianGovernorate | undefined;
    notes?: string | undefined;
    lastContactDate?: string | undefined;
}, {
    id: string;
    email: string;
    phone: string;
    createdAt: string;
    updatedAt: string;
    name: string;
    mood: ClientMood;
    salesRepId: string;
    website?: string | undefined;
    company?: string | undefined;
    address?: string | undefined;
    governorate?: EgyptianGovernorate | undefined;
    notes?: string | undefined;
    totalProjects?: number | undefined;
    totalRevenue?: number | undefined;
    lastContactDate?: string | undefined;
}>;
export type Client = z.infer<typeof ClientSchema>;
export declare const CreateClientSchema: z.ZodObject<Omit<{
    id: z.ZodString;
    name: z.ZodString;
    email: z.ZodString;
    phone: z.ZodString;
    company: z.ZodOptional<z.ZodString>;
    website: z.ZodOptional<z.ZodString>;
    address: z.ZodOptional<z.ZodString>;
    governorate: z.ZodOptional<z.ZodNativeEnum<typeof EgyptianGovernorate>>;
    mood: z.ZodNativeEnum<typeof ClientMood>;
    salesRepId: z.ZodString;
    notes: z.ZodOptional<z.ZodString>;
    totalProjects: z.ZodDefault<z.ZodNumber>;
    totalRevenue: z.ZodDefault<z.ZodNumber>;
    lastContactDate: z.ZodOptional<z.ZodString>;
    createdAt: z.ZodString;
    updatedAt: z.ZodString;
}, "id" | "createdAt" | "updatedAt" | "totalProjects" | "totalRevenue">, "strip", z.ZodTypeAny, {
    email: string;
    phone: string;
    name: string;
    mood: ClientMood;
    salesRepId: string;
    website?: string | undefined;
    company?: string | undefined;
    address?: string | undefined;
    governorate?: EgyptianGovernorate | undefined;
    notes?: string | undefined;
    lastContactDate?: string | undefined;
}, {
    email: string;
    phone: string;
    name: string;
    mood: ClientMood;
    salesRepId: string;
    website?: string | undefined;
    company?: string | undefined;
    address?: string | undefined;
    governorate?: EgyptianGovernorate | undefined;
    notes?: string | undefined;
    lastContactDate?: string | undefined;
}>;
export type CreateClientData = z.infer<typeof CreateClientSchema>;
export declare const UpdateClientSchema: z.ZodObject<{
    email: z.ZodOptional<z.ZodString>;
    phone: z.ZodOptional<z.ZodString>;
    website: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    name: z.ZodOptional<z.ZodString>;
    company: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    address: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    governorate: z.ZodOptional<z.ZodOptional<z.ZodNativeEnum<typeof EgyptianGovernorate>>>;
    mood: z.ZodOptional<z.ZodNativeEnum<typeof ClientMood>>;
    salesRepId: z.ZodOptional<z.ZodString>;
    notes: z.ZodOptional<z.ZodOptional<z.ZodString>>;
    lastContactDate: z.ZodOptional<z.ZodOptional<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    email?: string | undefined;
    phone?: string | undefined;
    website?: string | undefined;
    name?: string | undefined;
    company?: string | undefined;
    address?: string | undefined;
    governorate?: EgyptianGovernorate | undefined;
    mood?: ClientMood | undefined;
    salesRepId?: string | undefined;
    notes?: string | undefined;
    lastContactDate?: string | undefined;
}, {
    email?: string | undefined;
    phone?: string | undefined;
    website?: string | undefined;
    name?: string | undefined;
    company?: string | undefined;
    address?: string | undefined;
    governorate?: EgyptianGovernorate | undefined;
    mood?: ClientMood | undefined;
    salesRepId?: string | undefined;
    notes?: string | undefined;
    lastContactDate?: string | undefined;
}>;
export type UpdateClientData = z.infer<typeof UpdateClientSchema>;
export declare const ClientCommunicationSchema: z.ZodObject<{
    id: z.ZodString;
    clientId: z.ZodString;
    type: z.ZodEnum<["call", "email", "whatsapp", "meeting", "other"]>;
    subject: z.ZodString;
    content: z.ZodString;
    userId: z.ZodString;
    createdAt: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
    type: "email" | "call" | "whatsapp" | "meeting" | "other";
    createdAt: string;
    clientId: string;
    subject: string;
    content: string;
    userId: string;
}, {
    id: string;
    type: "email" | "call" | "whatsapp" | "meeting" | "other";
    createdAt: string;
    clientId: string;
    subject: string;
    content: string;
    userId: string;
}>;
export type ClientCommunication = z.infer<typeof ClientCommunicationSchema>;
