"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientCommunicationSchema = exports.UpdateClientSchema = exports.CreateClientSchema = exports.ClientSchema = exports.EgyptianGovernorate = exports.ClientMood = void 0;
const zod_1 = require("zod");
// Client Mood
var ClientMood;
(function (ClientMood) {
    ClientMood["HAPPY"] = "happy";
    ClientMood["NEUTRAL"] = "neutral";
    ClientMood["CONCERNED"] = "concerned";
    ClientMood["ANGRY"] = "angry";
})(ClientMood || (exports.ClientMood = ClientMood = {}));
// Egyptian Governorates
var EgyptianGovernorate;
(function (EgyptianGovernorate) {
    EgyptianGovernorate["CAIRO"] = "cairo";
    EgyptianGovernorate["ALEXANDRIA"] = "alexandria";
    EgyptianGovernorate["GIZA"] = "giza";
    EgyptianGovernorate["QALYUBIA"] = "qalyubia";
    EgyptianGovernorate["PORT_SAID"] = "port_said";
    EgyptianGovernorate["SUEZ"] = "suez";
    EgyptianGovernorate["LUXOR"] = "luxor";
    EgyptianGovernorate["ASWAN"] = "aswan";
    EgyptianGovernorate["ASYUT"] = "asyut";
    EgyptianGovernorate["BEHEIRA"] = "beheira";
    EgyptianGovernorate["BENI_SUEF"] = "beni_suef";
    EgyptianGovernorate["DAKAHLIA"] = "dakahlia";
    EgyptianGovernorate["DAMIETTA"] = "damietta";
    EgyptianGovernorate["FAYYUM"] = "fayyum";
    EgyptianGovernorate["GHARBIA"] = "gharbia";
    EgyptianGovernorate["ISMAILIA"] = "ismailia";
    EgyptianGovernorate["KAFR_EL_SHEIKH"] = "kafr_el_sheikh";
    EgyptianGovernorate["MATROUH"] = "matrouh";
    EgyptianGovernorate["MINYA"] = "minya";
    EgyptianGovernorate["MONUFIA"] = "monufia";
    EgyptianGovernorate["NEW_VALLEY"] = "new_valley";
    EgyptianGovernorate["NORTH_SINAI"] = "north_sinai";
    EgyptianGovernorate["QENA"] = "qena";
    EgyptianGovernorate["RED_SEA"] = "red_sea";
    EgyptianGovernorate["SHARQIA"] = "sharqia";
    EgyptianGovernorate["SOHAG"] = "sohag";
    EgyptianGovernorate["SOUTH_SINAI"] = "south_sinai";
})(EgyptianGovernorate || (exports.EgyptianGovernorate = EgyptianGovernorate = {}));
// Client Schema
exports.ClientSchema = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string(),
    email: zod_1.z.string().email(),
    phone: zod_1.z.string(),
    company: zod_1.z.string().optional(),
    website: zod_1.z.string().url().optional(),
    address: zod_1.z.string().optional(),
    governorate: zod_1.z.nativeEnum(EgyptianGovernorate).optional(),
    mood: zod_1.z.nativeEnum(ClientMood),
    salesRepId: zod_1.z.string(),
    notes: zod_1.z.string().optional(),
    totalProjects: zod_1.z.number().default(0),
    totalRevenue: zod_1.z.number().default(0),
    lastContactDate: zod_1.z.string().optional(),
    createdAt: zod_1.z.string(),
    updatedAt: zod_1.z.string(),
});
// Create Client Schema
exports.CreateClientSchema = exports.ClientSchema.omit({
    id: true,
    createdAt: true,
    updatedAt: true,
    totalProjects: true,
    totalRevenue: true,
});
// Update Client Schema
exports.UpdateClientSchema = exports.CreateClientSchema.partial();
// Client Communication Schema
exports.ClientCommunicationSchema = zod_1.z.object({
    id: zod_1.z.string(),
    clientId: zod_1.z.string(),
    type: zod_1.z.enum(['call', 'email', 'whatsapp', 'meeting', 'other']),
    subject: zod_1.z.string(),
    content: zod_1.z.string(),
    userId: zod_1.z.string(),
    createdAt: zod_1.z.string(),
});
