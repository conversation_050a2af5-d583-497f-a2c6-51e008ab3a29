"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenResponseSchema = exports.RegisterSchema = exports.LoginSchema = exports.UserSchema = exports.UserStatus = exports.UserRole = void 0;
const zod_1 = require("zod");
// User Roles
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "admin";
    UserRole["SALES_MANAGER"] = "sales_manager";
    UserRole["MEDIA_BUYER"] = "media_buyer";
    UserRole["DEVELOPER"] = "developer";
    UserRole["DESIGNER"] = "designer";
    UserRole["WORDPRESS_DEVELOPER"] = "wordpress_developer";
})(UserRole || (exports.UserRole = UserRole = {}));
// User Status
var UserStatus;
(function (UserStatus) {
    UserStatus["ACTIVE"] = "active";
    UserStatus["INACTIVE"] = "inactive";
    UserStatus["SUSPENDED"] = "suspended";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
// User Schema
exports.UserSchema = zod_1.z.object({
    id: zod_1.z.string(),
    email: zod_1.z.string().email(),
    firstName: zod_1.z.string(),
    lastName: zod_1.z.string(),
    role: zod_1.z.nativeEnum(UserRole),
    status: zod_1.z.nativeEnum(UserStatus),
    avatar: zod_1.z.string().optional(),
    phone: zod_1.z.string().optional(),
    createdAt: zod_1.z.string(),
    updatedAt: zod_1.z.string(),
    lastLoginAt: zod_1.z.string().optional(),
});
// Authentication Schemas
exports.LoginSchema = zod_1.z.object({
    email: zod_1.z.string().email('البريد الإلكتروني غير صحيح'),
    password: zod_1.z.string().min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
});
exports.RegisterSchema = zod_1.z.object({
    email: zod_1.z.string().email('البريد الإلكتروني غير صحيح'),
    password: zod_1.z.string().min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'),
    confirmPassword: zod_1.z.string(),
    firstName: zod_1.z.string().min(2, 'الاسم الأول مطلوب'),
    lastName: zod_1.z.string().min(2, 'الاسم الأخير مطلوب'),
    role: zod_1.z.nativeEnum(UserRole),
}).refine((data) => data.password === data.confirmPassword, {
    message: 'كلمات المرور غير متطابقة',
    path: ['confirmPassword'],
});
// JWT Token Response
exports.TokenResponseSchema = zod_1.z.object({
    access: zod_1.z.string(),
    refresh: zod_1.z.string(),
    user: exports.UserSchema,
});
