"use strict";
// Utility functions for MTBRMG ERP
Object.defineProperty(exports, "__esModule", { value: true });
exports.truncateText = exports.generateId = exports.debounce = exports.getStatusColor = exports.calculateProgress = exports.getInitials = exports.validateEgyptianPhone = exports.formatRelativeTime = exports.formatDate = exports.formatCurrency = void 0;
/**
 * Format currency in Egyptian Pounds
 */
const formatCurrency = (amount, currency = 'EGP') => {
    const formatter = new Intl.NumberFormat('ar-EG', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
    });
    return formatter.format(amount);
};
exports.formatCurrency = formatCurrency;
/**
 * Format date in Arabic locale
 */
const formatDate = (date, options) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const defaultOptions = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        ...options,
    };
    return new Intl.DateTimeFormat('ar-EG', defaultOptions).format(dateObj);
};
exports.formatDate = formatDate;
/**
 * Format relative time in Arabic
 */
const formatRelativeTime = (date) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
    if (diffInSeconds < 60) {
        return 'منذ لحظات';
    }
    else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `منذ ${minutes} دقيقة`;
    }
    else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `منذ ${hours} ساعة`;
    }
    else if (diffInSeconds < 2592000) {
        const days = Math.floor(diffInSeconds / 86400);
        return `منذ ${days} يوم`;
    }
    else {
        return (0, exports.formatDate)(dateObj);
    }
};
exports.formatRelativeTime = formatRelativeTime;
/**
 * Validate Egyptian phone number
 */
const validateEgyptianPhone = (phone) => {
    // Egyptian phone number patterns
    const patterns = [
        /^(\+20|0020|20)?1[0125]\d{8}$/, // Mobile numbers
        /^(\+20|0020|20)?[23]\d{7}$/, // Landline numbers
    ];
    return patterns.some(pattern => pattern.test(phone.replace(/\s+/g, '')));
};
exports.validateEgyptianPhone = validateEgyptianPhone;
/**
 * Generate initials from name
 */
const getInitials = (name) => {
    return name
        .split(' ')
        .map(word => word.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);
};
exports.getInitials = getInitials;
/**
 * Calculate progress percentage
 */
const calculateProgress = (completed, total) => {
    if (total === 0)
        return 0;
    return Math.round((completed / total) * 100);
};
exports.calculateProgress = calculateProgress;
/**
 * Get status color based on status value
 */
const getStatusColor = (status) => {
    const statusColors = {
        // Project statuses
        planning: 'bg-blue-100 text-blue-800',
        development: 'bg-yellow-100 text-yellow-800',
        testing: 'bg-orange-100 text-orange-800',
        deployment: 'bg-purple-100 text-purple-800',
        maintenance: 'bg-gray-100 text-gray-800',
        completed: 'bg-green-100 text-green-800',
        cancelled: 'bg-red-100 text-red-800',
        // Task statuses
        todo: 'bg-gray-100 text-gray-800',
        in_progress: 'bg-blue-100 text-blue-800',
        review: 'bg-yellow-100 text-yellow-800',
        // Priority levels
        low: 'bg-green-100 text-green-800',
        medium: 'bg-yellow-100 text-yellow-800',
        high: 'bg-orange-100 text-orange-800',
        urgent: 'bg-red-100 text-red-800',
        // Client moods
        happy: 'bg-green-100 text-green-800',
        neutral: 'bg-gray-100 text-gray-800',
        concerned: 'bg-yellow-100 text-yellow-800',
        angry: 'bg-red-100 text-red-800',
    };
    return statusColors[status] || 'bg-gray-100 text-gray-800';
};
exports.getStatusColor = getStatusColor;
/**
 * Debounce function
 */
const debounce = (func, wait) => {
    let timeout;
    return (...args) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
    };
};
exports.debounce = debounce;
/**
 * Generate random ID
 */
const generateId = () => {
    return Math.random().toString(36).substr(2, 9);
};
exports.generateId = generateId;
/**
 * Truncate text with ellipsis
 */
const truncateText = (text, maxLength) => {
    if (text.length <= maxLength)
        return text;
    return text.substr(0, maxLength) + '...';
};
exports.truncateText = truncateText;
