"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FILE_UPLOAD_LIMITS = exports.DEFAULT_PAGINATION = exports.TASK_CATEGORY_HOURS = exports.GOVERNORATE_LABELS = exports.ROLE_PERMISSIONS = exports.API_ENDPOINTS = void 0;
// API Endpoints
exports.API_ENDPOINTS = {
    AUTH: {
        LOGIN: '/auth/login',
        REGISTER: '/auth/register',
        REFRESH: '/auth/refresh',
        LOGOUT: '/auth/logout',
        PROFILE: '/auth/profile',
        CHANGE_PASSWORD: '/auth/change-password',
    },
    USERS: {
        LIST: '/users',
        CREATE: '/users',
        UPDATE: (id) => `/users/${id}`,
        DELETE: (id) => `/users/${id}`,
        PROFILE: (id) => `/users/${id}/profile`,
    },
    PROJECTS: {
        LIST: '/projects',
        CREATE: '/projects',
        UPDATE: (id) => `/projects/${id}`,
        DELETE: (id) => `/projects/${id}`,
        DETAILS: (id) => `/projects/${id}`,
    },
    CLIENTS: {
        LIST: '/clients',
        CREATE: '/clients',
        UPDATE: (id) => `/clients/${id}`,
        DELETE: (id) => `/clients/${id}`,
        DETAILS: (id) => `/clients/${id}`,
        COMMUNICATIONS: (id) => `/clients/${id}/communications`,
    },
    TASKS: {
        LIST: '/tasks',
        CREATE: '/tasks',
        UPDATE: (id) => `/tasks/${id}`,
        DELETE: (id) => `/tasks/${id}`,
        DETAILS: (id) => `/tasks/${id}`,
        TIME_LOGS: (id) => `/tasks/${id}/time-logs`,
        COMMENTS: (id) => `/tasks/${id}/comments`,
    },
};
// Role Permissions
exports.ROLE_PERMISSIONS = {
    admin: ['*'], // All permissions
    sales_manager: [
        'clients:read',
        'clients:write',
        'projects:read',
        'projects:write',
        'tasks:read',
        'analytics:read',
    ],
    media_buyer: [
        'clients:read',
        'projects:read',
        'tasks:read',
        'tasks:write',
        'analytics:read',
    ],
    developer: [
        'projects:read',
        'projects:write',
        'tasks:read',
        'tasks:write',
    ],
    designer: [
        'projects:read',
        'projects:write',
        'tasks:read',
        'tasks:write',
    ],
    wordpress_developer: [
        'projects:read',
        'projects:write',
        'tasks:read',
        'tasks:write',
    ],
};
// Egyptian Governorate Labels (Arabic)
exports.GOVERNORATE_LABELS = {
    cairo: 'القاهرة',
    alexandria: 'الإسكندرية',
    giza: 'الجيزة',
    qalyubia: 'القليوبية',
    port_said: 'بورسعيد',
    suez: 'السويس',
    luxor: 'الأقصر',
    aswan: 'أسوان',
    asyut: 'أسيوط',
    beheira: 'البحيرة',
    beni_suef: 'بني سويف',
    dakahlia: 'الدقهلية',
    damietta: 'دمياط',
    fayyum: 'الفيوم',
    gharbia: 'الغربية',
    ismailia: 'الإسماعيلية',
    kafr_el_sheikh: 'كفر الشيخ',
    matrouh: 'مطروح',
    minya: 'المنيا',
    monufia: 'المنوفية',
    new_valley: 'الوادي الجديد',
    north_sinai: 'شمال سيناء',
    qena: 'قنا',
    red_sea: 'البحر الأحمر',
    sharqia: 'الشرقية',
    sohag: 'سوهاج',
    south_sinai: 'جنوب سيناء',
};
// Task Category Time Estimates
exports.TASK_CATEGORY_HOURS = {
    light: { min: 1, max: 4, default: 2 },
    medium: { min: 4, max: 8, default: 6 },
    extreme: { min: 8, max: 24, default: 12 },
};
// Default Pagination
exports.DEFAULT_PAGINATION = {
    page: 1,
    limit: 20,
};
// File Upload Limits
exports.FILE_UPLOAD_LIMITS = {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ],
};
