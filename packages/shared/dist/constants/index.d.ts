export declare const API_ENDPOINTS: {
    readonly AUTH: {
        readonly LOGIN: "/auth/login";
        readonly REGISTER: "/auth/register";
        readonly REFRESH: "/auth/refresh";
        readonly LOGOUT: "/auth/logout";
        readonly PROFILE: "/auth/profile";
        readonly CHANGE_PASSWORD: "/auth/change-password";
    };
    readonly USERS: {
        readonly LIST: "/users";
        readonly CREATE: "/users";
        readonly UPDATE: (id: string) => string;
        readonly DELETE: (id: string) => string;
        readonly PROFILE: (id: string) => string;
    };
    readonly PROJECTS: {
        readonly LIST: "/projects";
        readonly CREATE: "/projects";
        readonly UPDATE: (id: string) => string;
        readonly DELETE: (id: string) => string;
        readonly DETAILS: (id: string) => string;
    };
    readonly CLIENTS: {
        readonly LIST: "/clients";
        readonly CREATE: "/clients";
        readonly UPDATE: (id: string) => string;
        readonly DELETE: (id: string) => string;
        readonly DETAILS: (id: string) => string;
        readonly COMMUNICATIONS: (id: string) => string;
    };
    readonly TASKS: {
        readonly LIST: "/tasks";
        readonly CREATE: "/tasks";
        readonly UPDATE: (id: string) => string;
        readonly DELETE: (id: string) => string;
        readonly DETAILS: (id: string) => string;
        readonly TIME_LOGS: (id: string) => string;
        readonly COMMENTS: (id: string) => string;
    };
};
export declare const ROLE_PERMISSIONS: {
    readonly admin: readonly ["*"];
    readonly sales_manager: readonly ["clients:read", "clients:write", "projects:read", "projects:write", "tasks:read", "analytics:read"];
    readonly media_buyer: readonly ["clients:read", "projects:read", "tasks:read", "tasks:write", "analytics:read"];
    readonly developer: readonly ["projects:read", "projects:write", "tasks:read", "tasks:write"];
    readonly designer: readonly ["projects:read", "projects:write", "tasks:read", "tasks:write"];
    readonly wordpress_developer: readonly ["projects:read", "projects:write", "tasks:read", "tasks:write"];
};
export declare const GOVERNORATE_LABELS: {
    readonly cairo: "القاهرة";
    readonly alexandria: "الإسكندرية";
    readonly giza: "الجيزة";
    readonly qalyubia: "القليوبية";
    readonly port_said: "بورسعيد";
    readonly suez: "السويس";
    readonly luxor: "الأقصر";
    readonly aswan: "أسوان";
    readonly asyut: "أسيوط";
    readonly beheira: "البحيرة";
    readonly beni_suef: "بني سويف";
    readonly dakahlia: "الدقهلية";
    readonly damietta: "دمياط";
    readonly fayyum: "الفيوم";
    readonly gharbia: "الغربية";
    readonly ismailia: "الإسماعيلية";
    readonly kafr_el_sheikh: "كفر الشيخ";
    readonly matrouh: "مطروح";
    readonly minya: "المنيا";
    readonly monufia: "المنوفية";
    readonly new_valley: "الوادي الجديد";
    readonly north_sinai: "شمال سيناء";
    readonly qena: "قنا";
    readonly red_sea: "البحر الأحمر";
    readonly sharqia: "الشرقية";
    readonly sohag: "سوهاج";
    readonly south_sinai: "جنوب سيناء";
};
export declare const TASK_CATEGORY_HOURS: {
    readonly light: {
        readonly min: 1;
        readonly max: 4;
        readonly default: 2;
    };
    readonly medium: {
        readonly min: 4;
        readonly max: 8;
        readonly default: 6;
    };
    readonly extreme: {
        readonly min: 8;
        readonly max: 24;
        readonly default: 12;
    };
};
export declare const DEFAULT_PAGINATION: {
    readonly page: 1;
    readonly limit: 20;
};
export declare const FILE_UPLOAD_LIMITS: {
    readonly maxSize: number;
    readonly allowedTypes: readonly ["image/jpeg", "image/png", "image/gif", "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"];
};
