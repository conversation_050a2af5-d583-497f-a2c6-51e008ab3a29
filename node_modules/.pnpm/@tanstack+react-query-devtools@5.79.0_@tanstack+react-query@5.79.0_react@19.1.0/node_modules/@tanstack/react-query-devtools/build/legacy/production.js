"use client";

// src/production.ts
import * as Devtools from "./ReactQueryDevtools.js";
import * as DevtoolsPanel from "./ReactQueryDevtoolsPanel.js";
var ReactQueryDevtools2 = Devtools.ReactQueryDevtools;
var ReactQueryDevtoolsPanel2 = DevtoolsPanel.ReactQueryDevtoolsPanel;
export {
  ReactQueryDevtools2 as ReactQueryDevtools,
  ReactQueryDevtoolsPanel2 as ReactQueryDevtoolsPanel
};
//# sourceMappingURL=production.js.map