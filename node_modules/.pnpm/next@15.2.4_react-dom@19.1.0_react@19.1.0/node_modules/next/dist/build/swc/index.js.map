{"version": 3, "sources": ["../../../src/build/swc/index.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-use-before-define */\nimport path from 'path'\nimport { pathToFileURL } from 'url'\nimport { arch, platform } from 'os'\nimport { platformArchTriples } from 'next/dist/compiled/@napi-rs/triples'\nimport * as Log from '../output/log'\nimport { getParserOptions } from './options'\nimport { eventSwcLoadFailure } from '../../telemetry/events/swc-load-failure'\nimport { patchIncorrectLockfile } from '../../lib/patch-incorrect-lockfile'\nimport { downloadNativeNextSwc, downloadWasmSwc } from '../../lib/download-swc'\nimport type {\n  NextConfigComplete,\n  TurboLoaderItem,\n  TurboRuleConfigItem,\n  TurboRuleConfigItemOptions,\n  TurboRuleConfigItemOrShortcut,\n} from '../../server/config-shared'\nimport { isDeepStrictEqual } from 'util'\nimport {\n  type DefineEnvPluginOptions,\n  getDefineEnv,\n} from '../webpack/plugins/define-env-plugin'\nimport { getReactCompilerLoader } from '../get-babel-loader-config'\nimport type {\n  NapiPartialProjectOptions,\n  NapiProjectOptions,\n} from './generated-native'\nimport type {\n  Binding,\n  DefineEnv,\n  Endpoint,\n  HmrIdentifiers,\n  Project,\n  ProjectOptions,\n  Route,\n  TurboEngineOptions,\n  TurbopackResult,\n  TurbopackStackFrame,\n  Update,\n  UpdateMessage,\n  WrittenEndpoint,\n} from './types'\nimport { TurbopackInternalError } from '../../shared/lib/turbopack/utils'\n\ntype RawBindings = typeof import('./generated-native')\ntype RawWasmBindings = typeof import('./generated-wasm') & {\n  default?(): Promise<typeof import('./generated-wasm')>\n}\n\nconst nextVersion = process.env.__NEXT_VERSION as string\n\nconst ArchName = arch()\nconst PlatformName = platform()\n\nfunction infoLog(...args: any[]) {\n  if (process.env.NEXT_PRIVATE_BUILD_WORKER) {\n    return\n  }\n  if (process.env.DEBUG) {\n    Log.info(...args)\n  }\n}\n\n/**\n * Based on napi-rs's target triples, returns triples that have corresponding next-swc binaries.\n */\nexport function getSupportedArchTriples(): Record<string, any> {\n  const { darwin, win32, linux, freebsd, android } = platformArchTriples\n\n  return {\n    darwin,\n    win32: {\n      arm64: win32.arm64,\n      ia32: win32.ia32.filter((triple) => triple.abi === 'msvc'),\n      x64: win32.x64.filter((triple) => triple.abi === 'msvc'),\n    },\n    linux: {\n      // linux[x64] includes `gnux32` abi, with x64 arch.\n      x64: linux.x64.filter((triple) => triple.abi !== 'gnux32'),\n      arm64: linux.arm64,\n      // This target is being deprecated, however we keep it in `knownDefaultWasmFallbackTriples` for now\n      arm: linux.arm,\n    },\n    // Below targets are being deprecated, however we keep it in `knownDefaultWasmFallbackTriples` for now\n    freebsd: {\n      x64: freebsd.x64,\n    },\n    android: {\n      arm64: android.arm64,\n      arm: android.arm,\n    },\n  }\n}\n\nconst triples = (() => {\n  const supportedArchTriples = getSupportedArchTriples()\n  const targetTriple = supportedArchTriples[PlatformName]?.[ArchName]\n\n  // If we have supported triple, return it right away\n  if (targetTriple) {\n    return targetTriple\n  }\n\n  // If there isn't corresponding target triple in `supportedArchTriples`, check if it's excluded from original raw triples\n  // Otherwise, it is completely unsupported platforms.\n  let rawTargetTriple = platformArchTriples[PlatformName]?.[ArchName]\n\n  if (rawTargetTriple) {\n    Log.warn(\n      `Trying to load next-swc for target triple ${rawTargetTriple}, but there next-swc does not have native bindings support`\n    )\n  } else {\n    Log.warn(\n      `Trying to load next-swc for unsupported platforms ${PlatformName}/${ArchName}`\n    )\n  }\n\n  return []\n})()\n\n// Allow to specify an absolute path to the custom turbopack binary to load.\n// If one of env variables is set, `loadNative` will try to use specified\n// binary instead. This is thin, naive interface\n// - `loadBindings` will not validate neither path nor the binary.\n//\n// Note these are internal flag: there's no stability, feature guarantee.\nconst __INTERNAL_CUSTOM_TURBOPACK_BINDINGS =\n  process.env.__INTERNAL_CUSTOM_TURBOPACK_BINDINGS\n\nfunction checkVersionMismatch(pkgData: any) {\n  const version = pkgData.version\n\n  if (version && version !== nextVersion) {\n    Log.warn(\n      `Mismatching @next/swc version, detected: ${version} while Next.js is on ${nextVersion}. Please ensure these match`\n    )\n  }\n}\n\n// These are the platforms we'll try to load wasm bindings first,\n// only try to load native bindings if loading wasm binding somehow fails.\n// Fallback to native binding is for migration period only,\n// once we can verify loading-wasm-first won't cause visible regressions,\n// we'll not include native bindings for these platform at all.\nconst knownDefaultWasmFallbackTriples = [\n  'x86_64-unknown-freebsd',\n  'aarch64-linux-android',\n  'arm-linux-androideabi',\n  'armv7-unknown-linux-gnueabihf',\n  'i686-pc-windows-msvc',\n  // WOA targets are TBD, while current userbase is small we may support it in the future\n  //'aarch64-pc-windows-msvc',\n]\n\n// The last attempt's error code returned when cjs require to native bindings fails.\n// If node.js throws an error without error code, this should be `unknown` instead of undefined.\n// For the wasm-first targets (`knownDefaultWasmFallbackTriples`) this will be `unsupported_target`.\nlet lastNativeBindingsLoadErrorCode:\n  | 'unknown'\n  | 'unsupported_target'\n  | string\n  | undefined = undefined\nlet nativeBindings: Binding\nlet wasmBindings: Binding\nlet downloadWasmPromise: any\nlet pendingBindings: any\nlet swcTraceFlushGuard: any\nlet downloadNativeBindingsPromise: Promise<void> | undefined = undefined\n\nexport const lockfilePatchPromise: { cur?: Promise<void> } = {}\n\nexport async function loadBindings(\n  useWasmBinary: boolean = false\n): Promise<Binding> {\n  // Increase Rust stack size as some npm packages being compiled need more than the default.\n  if (!process.env.RUST_MIN_STACK) {\n    process.env.RUST_MIN_STACK = '8388608'\n  }\n\n  if (pendingBindings) {\n    return pendingBindings\n  }\n\n  // rust needs stdout to be blocking, otherwise it will throw an error (on macOS at least) when writing a lot of data (logs) to it\n  // see https://github.com/napi-rs/napi-rs/issues/1630\n  // and https://github.com/nodejs/node/blob/main/doc/api/process.md#a-note-on-process-io\n  if (process.stdout._handle != null) {\n    // @ts-ignore\n    process.stdout._handle.setBlocking?.(true)\n  }\n  if (process.stderr._handle != null) {\n    // @ts-ignore\n    process.stderr._handle.setBlocking?.(true)\n  }\n\n  pendingBindings = new Promise(async (resolve, _reject) => {\n    if (!lockfilePatchPromise.cur) {\n      // always run lockfile check once so that it gets patched\n      // even if it doesn't fail to load locally\n      lockfilePatchPromise.cur = patchIncorrectLockfile(process.cwd()).catch(\n        console.error\n      )\n    }\n\n    let attempts: any[] = []\n    const disableWasmFallback = process.env.NEXT_DISABLE_SWC_WASM\n    const unsupportedPlatform = triples.some(\n      (triple: any) =>\n        !!triple?.raw && knownDefaultWasmFallbackTriples.includes(triple.raw)\n    )\n    const isWebContainer = process.versions.webcontainer\n    // Normal execution relies on the param `useWasmBinary` flag to load, but\n    // in certain cases where there isn't a native binary we always load wasm fallback first.\n    const shouldLoadWasmFallbackFirst =\n      (!disableWasmFallback && useWasmBinary) ||\n      unsupportedPlatform ||\n      isWebContainer\n\n    if (!unsupportedPlatform && useWasmBinary) {\n      Log.warn(\n        `experimental.useWasmBinary is not an option for supported platform ${PlatformName}/${ArchName} and will be ignored.`\n      )\n    }\n\n    if (shouldLoadWasmFallbackFirst) {\n      lastNativeBindingsLoadErrorCode = 'unsupported_target'\n      const fallbackBindings = await tryLoadWasmWithFallback(attempts)\n      if (fallbackBindings) {\n        return resolve(fallbackBindings)\n      }\n    }\n\n    // Trickle down loading `fallback` bindings:\n    //\n    // - First, try to load native bindings installed in node_modules.\n    // - If that fails with `ERR_MODULE_NOT_FOUND`, treat it as case of https://github.com/npm/cli/issues/4828\n    // that host system where generated package lock is not matching to the guest system running on, try to manually\n    // download corresponding target triple and load it. This won't be triggered if native bindings are failed to load\n    // with other reasons than `ERR_MODULE_NOT_FOUND`.\n    // - Lastly, falls back to wasm binding where possible.\n    try {\n      return resolve(loadNative())\n    } catch (a) {\n      if (\n        Array.isArray(a) &&\n        a.every((m) => m.includes('it was not installed'))\n      ) {\n        let fallbackBindings = await tryLoadNativeWithFallback(attempts)\n\n        if (fallbackBindings) {\n          return resolve(fallbackBindings)\n        }\n      }\n\n      attempts = attempts.concat(a)\n    }\n\n    // For these platforms we already tried to load wasm and failed, skip reattempt\n    if (!shouldLoadWasmFallbackFirst && !disableWasmFallback) {\n      const fallbackBindings = await tryLoadWasmWithFallback(attempts)\n      if (fallbackBindings) {\n        return resolve(fallbackBindings)\n      }\n    }\n\n    logLoadFailure(attempts, true)\n  })\n  return pendingBindings\n}\n\nasync function tryLoadNativeWithFallback(attempts: Array<string>) {\n  const nativeBindingsDirectory = path.join(\n    path.dirname(require.resolve('next/package.json')),\n    'next-swc-fallback'\n  )\n\n  if (!downloadNativeBindingsPromise) {\n    downloadNativeBindingsPromise = downloadNativeNextSwc(\n      nextVersion,\n      nativeBindingsDirectory,\n      triples.map((triple: any) => triple.platformArchABI)\n    )\n  }\n  await downloadNativeBindingsPromise\n\n  try {\n    return loadNative(nativeBindingsDirectory)\n  } catch (a: any) {\n    attempts.push(...[].concat(a))\n  }\n\n  return undefined\n}\n\nasync function tryLoadWasmWithFallback(attempts: any[]) {\n  try {\n    let bindings = await loadWasm('')\n    // @ts-expect-error TODO: this event has a wrong type.\n    eventSwcLoadFailure({\n      wasm: 'enabled',\n      nativeBindingsErrorCode: lastNativeBindingsLoadErrorCode,\n    })\n    return bindings\n  } catch (a: any) {\n    attempts.push(...[].concat(a))\n  }\n\n  try {\n    // if not installed already download wasm package on-demand\n    // we download to a custom directory instead of to node_modules\n    // as node_module import attempts are cached and can't be re-attempted\n    // x-ref: https://github.com/nodejs/modules/issues/307\n    const wasmDirectory = path.join(\n      path.dirname(require.resolve('next/package.json')),\n      'wasm'\n    )\n    if (!downloadWasmPromise) {\n      downloadWasmPromise = downloadWasmSwc(nextVersion, wasmDirectory)\n    }\n    await downloadWasmPromise\n    let bindings = await loadWasm(wasmDirectory)\n    // @ts-expect-error TODO: this event has a wrong type.\n    eventSwcLoadFailure({\n      wasm: 'fallback',\n      nativeBindingsErrorCode: lastNativeBindingsLoadErrorCode,\n    })\n\n    // still log native load attempts so user is\n    // aware it failed and should be fixed\n    for (const attempt of attempts) {\n      Log.warn(attempt)\n    }\n    return bindings\n  } catch (a: any) {\n    attempts.push(...[].concat(a))\n  }\n}\n\nfunction loadBindingsSync() {\n  let attempts: any[] = []\n  try {\n    return loadNative()\n  } catch (a) {\n    attempts = attempts.concat(a)\n  }\n\n  // we can leverage the wasm bindings if they are already\n  // loaded\n  if (wasmBindings) {\n    return wasmBindings\n  }\n\n  logLoadFailure(attempts)\n  throw new Error('Failed to load bindings', { cause: attempts })\n}\n\nlet loggingLoadFailure = false\n\nfunction logLoadFailure(attempts: any, triedWasm = false) {\n  // make sure we only emit the event and log the failure once\n  if (loggingLoadFailure) return\n  loggingLoadFailure = true\n\n  for (let attempt of attempts) {\n    Log.warn(attempt)\n  }\n\n  // @ts-expect-error TODO: this event has a wrong type.\n  eventSwcLoadFailure({\n    wasm: triedWasm ? 'failed' : undefined,\n    nativeBindingsErrorCode: lastNativeBindingsLoadErrorCode,\n  })\n    .then(() => lockfilePatchPromise.cur || Promise.resolve())\n    .finally(() => {\n      Log.error(\n        `Failed to load SWC binary for ${PlatformName}/${ArchName}, see more info here: https://nextjs.org/docs/messages/failed-loading-swc`\n      )\n      process.exit(1)\n    })\n}\n\ntype RustifiedEnv = { name: string; value: string }[]\n\nexport function createDefineEnv({\n  isTurbopack,\n  clientRouterFilters,\n  config,\n  dev,\n  distDir,\n  fetchCacheKeyPrefix,\n  hasRewrites,\n  middlewareMatchers,\n}: Omit<\n  DefineEnvPluginOptions,\n  'isClient' | 'isNodeOrEdgeCompilation' | 'isEdgeServer' | 'isNodeServer'\n>): DefineEnv {\n  let defineEnv: DefineEnv = {\n    client: [],\n    edge: [],\n    nodejs: [],\n  }\n\n  for (const variant of Object.keys(defineEnv) as (keyof typeof defineEnv)[]) {\n    defineEnv[variant] = rustifyEnv(\n      getDefineEnv({\n        isTurbopack,\n        clientRouterFilters,\n        config,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix,\n        hasRewrites,\n        isClient: variant === 'client',\n        isEdgeServer: variant === 'edge',\n        isNodeOrEdgeCompilation: variant === 'nodejs' || variant === 'edge',\n        isNodeServer: variant === 'nodejs',\n        middlewareMatchers,\n      })\n    )\n  }\n\n  return defineEnv\n}\n\nfunction rustifyEnv(env: Record<string, string>): RustifiedEnv {\n  return Object.entries(env)\n    .filter(([_, value]) => value != null)\n    .map(([name, value]) => ({\n      name,\n      value,\n    }))\n}\n\n// TODO(sokra) Support wasm option.\nfunction bindingToApi(\n  binding: RawBindings,\n  _wasm: boolean\n): Binding['turbo']['createProject'] {\n  type NativeFunction<T> = (\n    callback: (err: Error, value: T) => void\n  ) => Promise<{ __napiType: 'RootTask' }>\n\n  const cancel = new (class Cancel extends Error {})()\n\n  /**\n   * Utility function to ensure all variants of an enum are handled.\n   */\n  function invariant(\n    never: never,\n    computeMessage: (arg: any) => string\n  ): never {\n    throw new Error(`Invariant: ${computeMessage(never)}`)\n  }\n\n  async function withErrorCause<T>(fn: () => Promise<T>): Promise<T> {\n    try {\n      return await fn()\n    } catch (nativeError: any) {\n      throw new TurbopackInternalError(nativeError)\n    }\n  }\n\n  /**\n   * Calls a native function and streams the result.\n   * If useBuffer is true, all values will be preserved, potentially buffered\n   * if consumed slower than produced. Else, only the latest value will be\n   * preserved.\n   */\n  function subscribe<T>(\n    useBuffer: boolean,\n    nativeFunction:\n      | NativeFunction<T>\n      | ((callback: (err: Error, value: T) => void) => Promise<void>)\n  ): AsyncIterableIterator<T> {\n    type BufferItem =\n      | { err: Error; value: undefined }\n      | { err: undefined; value: T }\n    // A buffer of produced items. This will only contain values if the\n    // consumer is slower than the producer.\n    let buffer: BufferItem[] = []\n    // A deferred value waiting for the next produced item. This will only\n    // exist if the consumer is faster than the producer.\n    let waiting:\n      | {\n          resolve: (value: T) => void\n          reject: (error: Error) => void\n        }\n      | undefined\n    let canceled = false\n\n    // The native function will call this every time it emits a new result. We\n    // either need to notify a waiting consumer, or buffer the new result until\n    // the consumer catches up.\n    function emitResult(err: Error | undefined, value: T | undefined) {\n      if (waiting) {\n        let { resolve, reject } = waiting\n        waiting = undefined\n        if (err) reject(err)\n        else resolve(value!)\n      } else {\n        const item = { err, value } as BufferItem\n        if (useBuffer) buffer.push(item)\n        else buffer[0] = item\n      }\n    }\n\n    async function* createIterator() {\n      const task = await withErrorCause<{ __napiType: 'RootTask' } | void>(() =>\n        nativeFunction(emitResult)\n      )\n      try {\n        while (!canceled) {\n          if (buffer.length > 0) {\n            const item = buffer.shift()!\n            if (item.err) throw item.err\n            yield item.value\n          } else {\n            // eslint-disable-next-line no-loop-func\n            yield new Promise<T>((resolve, reject) => {\n              waiting = { resolve, reject }\n            })\n          }\n        }\n      } catch (e) {\n        if (e === cancel) return\n        if (e instanceof Error) {\n          throw new TurbopackInternalError(e)\n        }\n        throw e\n      } finally {\n        if (task) {\n          binding.rootTaskDispose(task)\n        }\n      }\n    }\n\n    const iterator = createIterator()\n    iterator.return = async () => {\n      canceled = true\n      if (waiting) waiting.reject(cancel)\n      return { value: undefined, done: true } as IteratorReturnResult<never>\n    }\n    return iterator\n  }\n\n  async function rustifyProjectOptions(\n    options: ProjectOptions\n  ): Promise<NapiProjectOptions> {\n    return {\n      ...options,\n      nextConfig: await serializeNextConfig(\n        options.nextConfig,\n        options.projectPath!\n      ),\n      jsConfig: JSON.stringify(options.jsConfig),\n      env: rustifyEnv(options.env),\n    }\n  }\n\n  async function rustifyPartialProjectOptions(\n    options: Partial<ProjectOptions>\n  ): Promise<NapiPartialProjectOptions> {\n    return {\n      ...options,\n      nextConfig:\n        options.nextConfig &&\n        (await serializeNextConfig(options.nextConfig, options.projectPath!)),\n      jsConfig: options.jsConfig && JSON.stringify(options.jsConfig),\n      env: options.env && rustifyEnv(options.env),\n    }\n  }\n\n  class ProjectImpl implements Project {\n    private readonly _nativeProject: { __napiType: 'Project' }\n\n    constructor(nativeProject: { __napiType: 'Project' }) {\n      this._nativeProject = nativeProject\n    }\n\n    async update(options: Partial<ProjectOptions>) {\n      await withErrorCause(async () =>\n        binding.projectUpdate(\n          this._nativeProject,\n          await rustifyPartialProjectOptions(options)\n        )\n      )\n    }\n\n    entrypointsSubscribe() {\n      type NapiEndpoint = { __napiType: 'Endpoint' }\n\n      type NapiEntrypoints = {\n        routes: NapiRoute[]\n        middleware?: NapiMiddleware\n        instrumentation?: NapiInstrumentation\n        pagesDocumentEndpoint: NapiEndpoint\n        pagesAppEndpoint: NapiEndpoint\n        pagesErrorEndpoint: NapiEndpoint\n      }\n\n      type NapiMiddleware = {\n        endpoint: NapiEndpoint\n        runtime: 'nodejs' | 'edge'\n        matcher?: string[]\n      }\n\n      type NapiInstrumentation = {\n        nodeJs: NapiEndpoint\n        edge: NapiEndpoint\n      }\n\n      type NapiRoute = {\n        pathname: string\n      } & (\n        | {\n            type: 'page'\n            htmlEndpoint: NapiEndpoint\n            dataEndpoint: NapiEndpoint\n          }\n        | {\n            type: 'page-api'\n            endpoint: NapiEndpoint\n          }\n        | {\n            type: 'app-page'\n            pages: {\n              originalName: string\n              htmlEndpoint: NapiEndpoint\n              rscEndpoint: NapiEndpoint\n            }[]\n          }\n        | {\n            type: 'app-route'\n            originalName: string\n            endpoint: NapiEndpoint\n          }\n        | {\n            type: 'conflict'\n          }\n      )\n\n      const subscription = subscribe<TurbopackResult<NapiEntrypoints>>(\n        false,\n        async (callback) =>\n          binding.projectEntrypointsSubscribe(this._nativeProject, callback)\n      )\n      return (async function* () {\n        for await (const entrypoints of subscription) {\n          const routes = new Map()\n          for (const { pathname, ...nativeRoute } of entrypoints.routes) {\n            let route: Route\n            const routeType = nativeRoute.type\n            switch (routeType) {\n              case 'page':\n                route = {\n                  type: 'page',\n                  htmlEndpoint: new EndpointImpl(nativeRoute.htmlEndpoint),\n                  dataEndpoint: new EndpointImpl(nativeRoute.dataEndpoint),\n                }\n                break\n              case 'page-api':\n                route = {\n                  type: 'page-api',\n                  endpoint: new EndpointImpl(nativeRoute.endpoint),\n                }\n                break\n              case 'app-page':\n                route = {\n                  type: 'app-page',\n                  pages: nativeRoute.pages.map((page) => ({\n                    originalName: page.originalName,\n                    htmlEndpoint: new EndpointImpl(page.htmlEndpoint),\n                    rscEndpoint: new EndpointImpl(page.rscEndpoint),\n                  })),\n                }\n                break\n              case 'app-route':\n                route = {\n                  type: 'app-route',\n                  originalName: nativeRoute.originalName,\n                  endpoint: new EndpointImpl(nativeRoute.endpoint),\n                }\n                break\n              case 'conflict':\n                route = {\n                  type: 'conflict',\n                }\n                break\n              default:\n                const _exhaustiveCheck: never = routeType\n                invariant(\n                  nativeRoute,\n                  () => `Unknown route type: ${_exhaustiveCheck}`\n                )\n            }\n            routes.set(pathname, route)\n          }\n          const napiMiddlewareToMiddleware = (middleware: NapiMiddleware) => ({\n            endpoint: new EndpointImpl(middleware.endpoint),\n            runtime: middleware.runtime,\n            matcher: middleware.matcher,\n          })\n          const middleware = entrypoints.middleware\n            ? napiMiddlewareToMiddleware(entrypoints.middleware)\n            : undefined\n          const napiInstrumentationToInstrumentation = (\n            instrumentation: NapiInstrumentation\n          ) => ({\n            nodeJs: new EndpointImpl(instrumentation.nodeJs),\n            edge: new EndpointImpl(instrumentation.edge),\n          })\n          const instrumentation = entrypoints.instrumentation\n            ? napiInstrumentationToInstrumentation(entrypoints.instrumentation)\n            : undefined\n          yield {\n            routes,\n            middleware,\n            instrumentation,\n            pagesDocumentEndpoint: new EndpointImpl(\n              entrypoints.pagesDocumentEndpoint\n            ),\n            pagesAppEndpoint: new EndpointImpl(entrypoints.pagesAppEndpoint),\n            pagesErrorEndpoint: new EndpointImpl(\n              entrypoints.pagesErrorEndpoint\n            ),\n            issues: entrypoints.issues,\n            diagnostics: entrypoints.diagnostics,\n          }\n        }\n      })()\n    }\n\n    hmrEvents(identifier: string) {\n      return subscribe<TurbopackResult<Update>>(true, async (callback) =>\n        binding.projectHmrEvents(this._nativeProject, identifier, callback)\n      )\n    }\n\n    hmrIdentifiersSubscribe() {\n      return subscribe<TurbopackResult<HmrIdentifiers>>(\n        false,\n        async (callback) =>\n          binding.projectHmrIdentifiersSubscribe(this._nativeProject, callback)\n      )\n    }\n\n    traceSource(\n      stackFrame: TurbopackStackFrame,\n      currentDirectoryFileUrl: string\n    ): Promise<TurbopackStackFrame | null> {\n      return binding.projectTraceSource(\n        this._nativeProject,\n        stackFrame,\n        currentDirectoryFileUrl\n      )\n    }\n\n    getSourceForAsset(filePath: string): Promise<string | null> {\n      return binding.projectGetSourceForAsset(this._nativeProject, filePath)\n    }\n\n    getSourceMap(filePath: string): Promise<string | null> {\n      return binding.projectGetSourceMap(this._nativeProject, filePath)\n    }\n\n    getSourceMapSync(filePath: string): string | null {\n      return binding.projectGetSourceMapSync(this._nativeProject, filePath)\n    }\n\n    updateInfoSubscribe(aggregationMs: number) {\n      return subscribe<TurbopackResult<UpdateMessage>>(true, async (callback) =>\n        binding.projectUpdateInfoSubscribe(\n          this._nativeProject,\n          aggregationMs,\n          callback\n        )\n      )\n    }\n\n    shutdown(): Promise<void> {\n      return binding.projectShutdown(this._nativeProject)\n    }\n\n    onExit(): Promise<void> {\n      return binding.projectOnExit(this._nativeProject)\n    }\n  }\n\n  class EndpointImpl implements Endpoint {\n    private readonly _nativeEndpoint: { __napiType: 'Endpoint' }\n\n    constructor(nativeEndpoint: { __napiType: 'Endpoint' }) {\n      this._nativeEndpoint = nativeEndpoint\n    }\n\n    async writeToDisk(): Promise<TurbopackResult<WrittenEndpoint>> {\n      return await withErrorCause(\n        () =>\n          binding.endpointWriteToDisk(this._nativeEndpoint) as Promise<\n            TurbopackResult<WrittenEndpoint>\n          >\n      )\n    }\n\n    async clientChanged(): Promise<AsyncIterableIterator<TurbopackResult<{}>>> {\n      const clientSubscription = subscribe<TurbopackResult>(\n        false,\n        async (callback) =>\n          binding.endpointClientChangedSubscribe(\n            await this._nativeEndpoint,\n            callback\n          )\n      )\n      await clientSubscription.next()\n      return clientSubscription\n    }\n\n    async serverChanged(\n      includeIssues: boolean\n    ): Promise<AsyncIterableIterator<TurbopackResult<{}>>> {\n      const serverSubscription = subscribe<TurbopackResult>(\n        false,\n        async (callback) =>\n          binding.endpointServerChangedSubscribe(\n            await this._nativeEndpoint,\n            includeIssues,\n            callback\n          )\n      )\n      await serverSubscription.next()\n      return serverSubscription\n    }\n  }\n\n  /**\n   * Returns a new copy of next.js config object to avoid mutating the original.\n   *\n   * Also it does some augmentation to the configuration as well, for example set the\n   * turbopack's rules if `experimental.reactCompilerOptions` is set.\n   */\n  function augmentNextConfig(\n    originalNextConfig: NextConfigComplete,\n    projectPath: string\n  ): Record<string, any> {\n    let nextConfig = { ...(originalNextConfig as any) }\n\n    const reactCompilerOptions = nextConfig.experimental?.reactCompiler\n\n    // It is not easy to set the rules inside of rust as resolving, and passing the context identical to the webpack\n    // config is bit hard, also we can reuse same codes between webpack config in here.\n    if (reactCompilerOptions) {\n      const ruleKeys = ['*.ts', '*.js', '*.jsx', '*.tsx']\n      if (\n        Object.keys(nextConfig?.experimental?.turbo?.rules ?? []).some((key) =>\n          ruleKeys.includes(key)\n        )\n      ) {\n        Log.warn(\n          `The React Compiler cannot be enabled automatically because 'experimental.turbo' contains a rule for '*.ts', '*.js', '*.jsx', and '*.tsx'. Remove this rule, or add 'babel-loader' and 'babel-plugin-react-compiler' to the Turbopack configuration manually.`\n        )\n      } else {\n        if (!nextConfig.experimental.turbo) {\n          nextConfig.experimental.turbo = {}\n        }\n\n        if (!nextConfig.experimental.turbo.rules) {\n          nextConfig.experimental.turbo.rules = {}\n        }\n\n        for (const key of ['*.ts', '*.js', '*.jsx', '*.tsx']) {\n          nextConfig.experimental.turbo.rules[key] = {\n            browser: {\n              foreign: false,\n              loaders: [\n                getReactCompilerLoader(\n                  originalNextConfig.experimental.reactCompiler,\n                  projectPath,\n                  nextConfig.dev,\n                  false,\n                  undefined\n                ),\n              ],\n            },\n          }\n        }\n      }\n    }\n\n    return nextConfig\n  }\n\n  async function serializeNextConfig(\n    nextConfig: NextConfigComplete,\n    projectPath: string\n  ): Promise<string> {\n    // Avoid mutating the existing `nextConfig` object.\n    let nextConfigSerializable = augmentNextConfig(nextConfig, projectPath)\n\n    nextConfigSerializable.generateBuildId =\n      await nextConfig.generateBuildId?.()\n\n    // TODO: these functions takes arguments, have to be supported in a different way\n    nextConfigSerializable.exportPathMap = {}\n    nextConfigSerializable.webpack = nextConfig.webpack && {}\n\n    if (nextConfigSerializable.experimental?.turbo?.rules) {\n      ensureLoadersHaveSerializableOptions(\n        nextConfigSerializable.experimental.turbo?.rules\n      )\n    }\n\n    nextConfigSerializable.modularizeImports =\n      nextConfigSerializable.modularizeImports\n        ? Object.fromEntries(\n            Object.entries<any>(nextConfigSerializable.modularizeImports).map(\n              ([mod, config]) => [\n                mod,\n                {\n                  ...config,\n                  transform:\n                    typeof config.transform === 'string'\n                      ? config.transform\n                      : Object.entries(config.transform).map(([key, value]) => [\n                          key,\n                          value,\n                        ]),\n                },\n              ]\n            )\n          )\n        : undefined\n\n    // loaderFile is an absolute path, we need it to be relative for turbopack.\n    if (nextConfigSerializable.images.loaderFile) {\n      nextConfigSerializable.images = {\n        ...nextConfig.images,\n        loaderFile:\n          './' + path.relative(projectPath, nextConfig.images.loaderFile),\n      }\n    }\n\n    return JSON.stringify(nextConfigSerializable, null, 2)\n  }\n\n  function ensureLoadersHaveSerializableOptions(\n    turbopackRules: Record<string, TurboRuleConfigItemOrShortcut>\n  ) {\n    for (const [glob, rule] of Object.entries(turbopackRules)) {\n      if (Array.isArray(rule)) {\n        checkLoaderItems(rule, glob)\n      } else {\n        checkConfigItem(rule, glob)\n      }\n    }\n\n    function checkConfigItem(rule: TurboRuleConfigItem, glob: string) {\n      if (!rule) return\n      if ('loaders' in rule) {\n        checkLoaderItems((rule as TurboRuleConfigItemOptions).loaders, glob)\n      } else {\n        for (const key in rule) {\n          const inner = rule[key]\n          if (typeof inner === 'object' && inner) {\n            checkConfigItem(inner, glob)\n          }\n        }\n      }\n    }\n\n    function checkLoaderItems(loaderItems: TurboLoaderItem[], glob: string) {\n      for (const loaderItem of loaderItems) {\n        if (\n          typeof loaderItem !== 'string' &&\n          !isDeepStrictEqual(loaderItem, JSON.parse(JSON.stringify(loaderItem)))\n        ) {\n          throw new Error(\n            `loader ${loaderItem.loader} for match \"${glob}\" does not have serializable options. Ensure that options passed are plain JavaScript objects and values.`\n          )\n        }\n      }\n    }\n  }\n\n  return async function createProject(\n    options: ProjectOptions,\n    turboEngineOptions\n  ) {\n    return new ProjectImpl(\n      await binding.projectNew(\n        await rustifyProjectOptions(options),\n        turboEngineOptions || {}\n      )\n    )\n  }\n}\n\nasync function loadWasm(importPath = '') {\n  if (wasmBindings) {\n    return wasmBindings\n  }\n\n  let attempts = []\n  for (let pkg of ['@next/swc-wasm-nodejs', '@next/swc-wasm-web']) {\n    try {\n      let pkgPath = pkg\n\n      if (importPath) {\n        // the import path must be exact when not in node_modules\n        pkgPath = path.join(importPath, pkg, 'wasm.js')\n      }\n      let bindings: RawWasmBindings = await import(\n        pathToFileURL(pkgPath).toString()\n      )\n      if (pkg === '@next/swc-wasm-web') {\n        bindings = await bindings.default!()\n      }\n      infoLog('next-swc build: wasm build @next/swc-wasm-web')\n\n      // Note wasm binary does not support async intefaces yet, all async\n      // interface coereces to sync interfaces.\n      wasmBindings = {\n        css: {\n          lightning: {\n            transform: function (_options: any) {\n              throw new Error(\n                '`css.lightning.transform` is not supported by the wasm bindings.'\n              )\n            },\n            transformStyleAttr: function (_options: any) {\n              throw new Error(\n                '`css.lightning.transformStyleAttr` is not supported by the wasm bindings.'\n              )\n            },\n          },\n        },\n        isWasm: true,\n        transform(src: string, options: any) {\n          // TODO: we can remove fallback to sync interface once new stable version of next-swc gets published (current v12.2)\n          return bindings?.transform\n            ? bindings.transform(src.toString(), options)\n            : Promise.resolve(bindings.transformSync(src.toString(), options))\n        },\n        transformSync(src: string, options: any) {\n          return bindings.transformSync(src.toString(), options)\n        },\n        minify(src: string, options: any) {\n          return bindings?.minify\n            ? bindings.minify(src.toString(), options)\n            : Promise.resolve(bindings.minifySync(src.toString(), options))\n        },\n        minifySync(src: string, options: any) {\n          return bindings.minifySync(src.toString(), options)\n        },\n        parse(src: string, options: any) {\n          return bindings?.parse\n            ? bindings.parse(src.toString(), options)\n            : Promise.resolve(bindings.parseSync(src.toString(), options))\n        },\n        getTargetTriple() {\n          return undefined\n        },\n        turbo: {\n          createProject: function (\n            _options: ProjectOptions,\n            _turboEngineOptions?: TurboEngineOptions | undefined\n          ): Promise<Project> {\n            throw new Error(\n              '`turbo.createProject` is not supported by the wasm bindings.'\n            )\n          },\n          startTurbopackTraceServer: function (_traceFilePath: string): void {\n            throw new Error(\n              '`turbo.startTurbopackTraceServer` is not supported by the wasm bindings.'\n            )\n          },\n        },\n        mdx: {\n          compile(src: string, options: any) {\n            return bindings.mdxCompile(src, getMdxOptions(options))\n          },\n          compileSync(src: string, options: any) {\n            return bindings.mdxCompileSync(src, getMdxOptions(options))\n          },\n        },\n      }\n      return wasmBindings\n    } catch (e: any) {\n      // Only log attempts for loading wasm when loading as fallback\n      if (importPath) {\n        if (e?.code === 'ERR_MODULE_NOT_FOUND') {\n          attempts.push(`Attempted to load ${pkg}, but it was not installed`)\n        } else {\n          attempts.push(\n            `Attempted to load ${pkg}, but an error occurred: ${e.message ?? e}`\n          )\n        }\n      }\n    }\n  }\n\n  throw attempts\n}\n\nfunction loadNative(importPath?: string) {\n  if (nativeBindings) {\n    return nativeBindings\n  }\n\n  const customBindings: RawBindings = !!__INTERNAL_CUSTOM_TURBOPACK_BINDINGS\n    ? require(__INTERNAL_CUSTOM_TURBOPACK_BINDINGS)\n    : null\n  let bindings: RawBindings = customBindings\n  let attempts: any[] = []\n\n  const NEXT_TEST_NATIVE_DIR = process.env.NEXT_TEST_NATIVE_DIR\n  for (const triple of triples) {\n    if (NEXT_TEST_NATIVE_DIR) {\n      try {\n        // Use the binary directly to skip `pnpm pack` for testing as it's slow because of the large native binary.\n        bindings = require(\n          `${NEXT_TEST_NATIVE_DIR}/next-swc.${triple.platformArchABI}.node`\n        )\n        infoLog(\n          'next-swc build: local built @next/swc from NEXT_TEST_NATIVE_DIR'\n        )\n        break\n      } catch (e) {}\n    } else {\n      try {\n        bindings = require(\n          `@next/swc/native/next-swc.${triple.platformArchABI}.node`\n        )\n        infoLog('next-swc build: local built @next/swc')\n        break\n      } catch (e) {}\n    }\n  }\n\n  if (!bindings) {\n    for (const triple of triples) {\n      let pkg = importPath\n        ? path.join(\n            importPath,\n            `@next/swc-${triple.platformArchABI}`,\n            `next-swc.${triple.platformArchABI}.node`\n          )\n        : `@next/swc-${triple.platformArchABI}`\n      try {\n        bindings = require(pkg)\n        if (!importPath) {\n          checkVersionMismatch(require(`${pkg}/package.json`))\n        }\n        break\n      } catch (e: any) {\n        if (e?.code === 'MODULE_NOT_FOUND') {\n          attempts.push(`Attempted to load ${pkg}, but it was not installed`)\n        } else {\n          attempts.push(\n            `Attempted to load ${pkg}, but an error occurred: ${e.message ?? e}`\n          )\n        }\n        lastNativeBindingsLoadErrorCode = e?.code ?? 'unknown'\n      }\n    }\n  }\n\n  if (bindings) {\n    nativeBindings = {\n      isWasm: false,\n      transform(src: string, options: any) {\n        const isModule =\n          typeof src !== 'undefined' &&\n          typeof src !== 'string' &&\n          !Buffer.isBuffer(src)\n        options = options || {}\n\n        if (options?.jsc?.parser) {\n          options.jsc.parser.syntax = options.jsc.parser.syntax ?? 'ecmascript'\n        }\n\n        return bindings.transform(\n          isModule ? JSON.stringify(src) : src,\n          isModule,\n          toBuffer(options)\n        )\n      },\n\n      transformSync(src: string, options: any) {\n        if (typeof src === 'undefined') {\n          throw new Error(\n            \"transformSync doesn't implement reading the file from filesystem\"\n          )\n        } else if (Buffer.isBuffer(src)) {\n          throw new Error(\n            \"transformSync doesn't implement taking the source code as Buffer\"\n          )\n        }\n        const isModule = typeof src !== 'string'\n        options = options || {}\n\n        if (options?.jsc?.parser) {\n          options.jsc.parser.syntax = options.jsc.parser.syntax ?? 'ecmascript'\n        }\n\n        return bindings.transformSync(\n          isModule ? JSON.stringify(src) : src,\n          isModule,\n          toBuffer(options)\n        )\n      },\n\n      minify(src: string, options: any) {\n        return bindings.minify(toBuffer(src), toBuffer(options ?? {}))\n      },\n\n      minifySync(src: string, options: any) {\n        return bindings.minifySync(toBuffer(src), toBuffer(options ?? {}))\n      },\n\n      parse(src: string, options: any) {\n        return bindings.parse(src, toBuffer(options ?? {}))\n      },\n\n      getTargetTriple: bindings.getTargetTriple,\n      initCustomTraceSubscriber: bindings.initCustomTraceSubscriber,\n      teardownTraceSubscriber: bindings.teardownTraceSubscriber,\n      turbo: {\n        createProject: bindingToApi(customBindings ?? bindings, false),\n        startTurbopackTraceServer(traceFilePath) {\n          Log.warn(\n            'Turbopack trace server started. View trace at https://turbo-trace-viewer.vercel.app/'\n          )\n          ;(customBindings ?? bindings).startTurbopackTraceServer(traceFilePath)\n        },\n      },\n      mdx: {\n        compile(src: string, options: any) {\n          return bindings.mdxCompile(src, toBuffer(getMdxOptions(options)))\n        },\n        compileSync(src: string, options: any) {\n          bindings.mdxCompileSync(src, toBuffer(getMdxOptions(options)))\n        },\n      },\n      css: {\n        lightning: {\n          transform(transformOptions: any) {\n            return bindings.lightningCssTransform(transformOptions)\n          },\n          transformStyleAttr(transformAttrOptions: any) {\n            return bindings.lightningCssTransformStyleAttribute(\n              transformAttrOptions\n            )\n          },\n        },\n      },\n    }\n    return nativeBindings\n  }\n\n  throw attempts\n}\n\n/// Build a mdx options object contains default values that\n/// can be parsed with serde_wasm_bindgen.\nfunction getMdxOptions(options: any = {}) {\n  return {\n    ...options,\n    development: options.development ?? false,\n    jsx: options.jsx ?? false,\n    mdxType: options.mdxType ?? 'commonMark',\n  }\n}\n\nfunction toBuffer(t: any) {\n  return Buffer.from(JSON.stringify(t))\n}\n\nexport async function isWasm(): Promise<boolean> {\n  let bindings = await loadBindings()\n  return bindings.isWasm\n}\n\nexport async function transform(src: string, options?: any): Promise<any> {\n  let bindings = await loadBindings()\n  return bindings.transform(src, options)\n}\n\nexport function transformSync(src: string, options?: any): any {\n  let bindings = loadBindingsSync()\n  return bindings.transformSync(src, options)\n}\n\nexport async function minify(\n  src: string,\n  options: any\n): Promise<{ code: string; map: any }> {\n  let bindings = await loadBindings()\n  return bindings.minify(src, options)\n}\n\nexport async function parse(src: string, options: any): Promise<any> {\n  let bindings = await loadBindings()\n  let parserOptions = getParserOptions(options)\n  return bindings\n    .parse(src, parserOptions)\n    .then((astStr: any) => JSON.parse(astStr))\n}\n\nexport function getBinaryMetadata() {\n  let bindings\n  try {\n    bindings = loadNative()\n  } catch (e) {\n    // Suppress exceptions, this fn allows to fail to load native bindings\n  }\n\n  return {\n    target: bindings?.getTargetTriple?.(),\n  }\n}\n\n/**\n * Initialize trace subscriber to emit traces.\n *\n */\nexport function initCustomTraceSubscriber(traceFileName?: string) {\n  if (swcTraceFlushGuard) {\n    // Wasm binary doesn't support trace emission\n    let bindings = loadNative()\n    swcTraceFlushGuard = bindings.initCustomTraceSubscriber?.(traceFileName)\n  }\n}\n\nfunction once(fn: () => void): () => void {\n  let executed = false\n\n  return function (): void {\n    if (!executed) {\n      executed = true\n\n      fn()\n    }\n  }\n}\n\n/**\n * Teardown swc's trace subscriber if there's an initialized flush guard exists.\n *\n * This is workaround to amend behavior with process.exit\n * (https://github.com/vercel/next.js/blob/4db8c49cc31e4fc182391fae6903fb5ef4e8c66e/packages/next/bin/next.ts#L134=)\n * seems preventing napi's cleanup hook execution (https://github.com/swc-project/swc/blob/main/crates/node/src/util.rs#L48-L51=),\n *\n * instead parent process manually drops guard when process gets signal to exit.\n */\nexport const teardownTraceSubscriber = once(() => {\n  try {\n    let bindings = loadNative()\n    if (swcTraceFlushGuard) {\n      bindings.teardownTraceSubscriber?.(swcTraceFlushGuard)\n    }\n  } catch (e) {\n    // Suppress exceptions, this fn allows to fail to load native bindings\n  }\n})\n"], "names": ["createDefineEnv", "getBinaryMetadata", "getSupportedArchTriples", "initCustomTraceSubscriber", "isWasm", "loadBindings", "lockfilePatchPromise", "minify", "parse", "teardownTraceSubscriber", "transform", "transformSync", "nextVersion", "process", "env", "__NEXT_VERSION", "Arch<PERSON>ame", "arch", "PlatformName", "platform", "infoLog", "args", "NEXT_PRIVATE_BUILD_WORKER", "DEBUG", "Log", "info", "darwin", "win32", "linux", "freebsd", "android", "platformArchTriples", "arm64", "ia32", "filter", "triple", "abi", "x64", "arm", "triples", "supportedArchTriples", "targetTriple", "rawTargetTriple", "warn", "__INTERNAL_CUSTOM_TURBOPACK_BINDINGS", "checkVersionMismatch", "pkgData", "version", "knownDefaultWasmFallbackTriples", "lastNativeBindingsLoadErrorCode", "undefined", "nativeBindings", "wasmBindings", "downloadWasmPromise", "pendingBindings", "swcTraceFlushGuard", "downloadNativeBindingsPromise", "useWasmBinary", "RUST_MIN_STACK", "stdout", "_handle", "setBlocking", "stderr", "Promise", "resolve", "_reject", "cur", "patchIncorrectLockfile", "cwd", "catch", "console", "error", "attempts", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NEXT_DISABLE_SWC_WASM", "unsupportedPlatform", "some", "raw", "includes", "isWebContainer", "versions", "webcontainer", "shouldLoadWasmFallbackFirst", "fallback<PERSON><PERSON><PERSON>", "tryLoadWasmWithFallback", "loadNative", "a", "Array", "isArray", "every", "m", "tryLoadNativeWithFallback", "concat", "logLoadFailure", "nativeBindingsDirectory", "path", "join", "dirname", "require", "downloadNativeNextSwc", "map", "platformArchABI", "push", "bindings", "loadWasm", "eventSwcLoadFailure", "wasm", "nativeBindingsErrorCode", "wasmDirectory", "downloadWasmSwc", "attempt", "loadBindingsSync", "Error", "cause", "loggingLoadFailure", "triedWasm", "then", "finally", "exit", "isTurbopack", "clientRouterFilters", "config", "dev", "distDir", "fetchCacheKeyPrefix", "hasRewrites", "middlewareMatchers", "defineEnv", "client", "edge", "nodejs", "variant", "Object", "keys", "rustifyEnv", "getDefineEnv", "isClient", "isEdgeServer", "isNodeOrEdgeCompilation", "isNodeServer", "entries", "_", "value", "name", "bindingToApi", "binding", "_wasm", "cancel", "Cancel", "invariant", "never", "computeMessage", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "nativeError", "TurbopackInternalError", "subscribe", "useBuffer", "nativeFunction", "buffer", "waiting", "canceled", "emitResult", "err", "reject", "item", "createIterator", "task", "length", "shift", "e", "rootTaskDispose", "iterator", "return", "done", "rustifyProjectOptions", "options", "nextConfig", "serializeNextConfig", "projectPath", "jsConfig", "JSON", "stringify", "rustifyPartialProjectOptions", "ProjectImpl", "constructor", "nativeProject", "_nativeProject", "update", "projectUpdate", "entrypointsSubscribe", "subscription", "callback", "projectEntrypointsSubscribe", "entrypoints", "routes", "Map", "pathname", "nativeRoute", "route", "routeType", "type", "htmlEndpoint", "EndpointImpl", "dataEndpoint", "endpoint", "pages", "page", "originalName", "rscEndpoint", "_exhaustiveCheck", "set", "napiMiddlewareToMiddleware", "middleware", "runtime", "matcher", "napiInstrumentationToInstrumentation", "instrumentation", "nodeJs", "pagesDocumentEndpoint", "pagesAppEndpoint", "pagesErrorEndpoint", "issues", "diagnostics", "hmrEvents", "identifier", "projectHmrEvents", "hmrIdentifiersSubscribe", "projectHmrIdentifiersSubscribe", "traceSource", "stackFrame", "currentDirectoryFileUrl", "projectTraceSource", "getSourceForAsset", "filePath", "projectGetSourceForAsset", "getSourceMap", "projectGetSourceMap", "getSourceMapSync", "projectGetSourceMapSync", "updateInfoSubscribe", "aggregationMs", "projectUpdateInfoSubscribe", "shutdown", "projectShutdown", "onExit", "projectOnExit", "nativeEndpoint", "_nativeEndpoint", "writeToDisk", "endpointWriteToDisk", "clientChanged", "clientSubscription", "endpointClientChangedSubscribe", "next", "serverChanged", "includeIssues", "serverSubscription", "endpointServerChangedSubscribe", "augmentNextConfig", "originalNextConfig", "reactCompilerOptions", "experimental", "reactCompiler", "rule<PERSON>eys", "turbo", "rules", "key", "browser", "foreign", "loaders", "getReactCompilerLoader", "nextConfigSerializable", "generateBuildId", "exportPathMap", "webpack", "ensureLoadersHaveSerializableOptions", "modularizeImports", "fromEntries", "mod", "images", "loaderFile", "relative", "turbopackRules", "glob", "rule", "checkLoaderItems", "checkConfigItem", "inner", "loaderItems", "loaderItem", "isDeepStrictEqual", "loader", "createProject", "turboEngineOptions", "projectNew", "importPath", "pkg", "pkgPath", "pathToFileURL", "toString", "default", "css", "lightning", "_options", "transformStyleAttr", "src", "minifySync", "parseSync", "getTargetTriple", "_turboEngineOptions", "startTurbopackTraceServer", "_traceFilePath", "mdx", "compile", "mdxCompile", "getMdxOptions", "compileSync", "mdxCompileSync", "code", "message", "customBindings", "NEXT_TEST_NATIVE_DIR", "isModule", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "jsc", "parser", "syntax", "<PERSON><PERSON><PERSON><PERSON>", "traceFilePath", "transformOptions", "lightningCssTransform", "transformAttrOptions", "lightningCssTransformStyleAttribute", "development", "jsx", "mdxType", "t", "from", "parserOptions", "getParserOptions", "astStr", "target", "traceFileName", "once", "executed"], "mappings": "AAAA,0DAA0D;;;;;;;;;;;;;;;;;;;;;;;;;IA+X1CA,eAAe;eAAfA;;IA65BAC,iBAAiB;eAAjBA;;IA1tCAC,uBAAuB;eAAvBA;;IA2uCAC,yBAAyB;eAAzBA;;IAhDMC,MAAM;eAANA;;IAllCAC,YAAY;eAAZA;;IAFTC,oBAAoB;eAApBA;;IAmmCSC,MAAM;eAANA;;IAQAC,KAAK;eAALA;;IAsDTC,uBAAuB;eAAvBA;;IAxESC,SAAS;eAATA;;IAKNC,aAAa;eAAbA;;;6DAtwCC;qBACa;oBACC;yBACK;6DACf;yBACY;gCACG;wCACG;6BACgB;sBAQrB;iCAI3B;sCACgC;uBAoBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvC,MAAMC,cAAcC,QAAQC,GAAG,CAACC,cAAc;AAE9C,MAAMC,WAAWC,IAAAA,QAAI;AACrB,MAAMC,eAAeC,IAAAA,YAAQ;AAE7B,SAASC,QAAQ,GAAGC,IAAW;IAC7B,IAAIR,QAAQC,GAAG,CAACQ,yBAAyB,EAAE;QACzC;IACF;IACA,IAAIT,QAAQC,GAAG,CAACS,KAAK,EAAE;QACrBC,KAAIC,IAAI,IAAIJ;IACd;AACF;AAKO,SAASnB;IACd,MAAM,EAAEwB,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE,GAAGC,4BAAmB;IAEtE,OAAO;QACLL;QACAC,OAAO;YACLK,OAAOL,MAAMK,KAAK;YAClBC,MAAMN,MAAMM,IAAI,CAACC,MAAM,CAAC,CAACC,SAAWA,OAAOC,GAAG,KAAK;YACnDC,KAAKV,MAAMU,GAAG,CAACH,MAAM,CAAC,CAACC,SAAWA,OAAOC,GAAG,KAAK;QACnD;QACAR,OAAO;YACL,mDAAmD;YACnDS,KAAKT,MAAMS,GAAG,CAACH,MAAM,CAAC,CAACC,SAAWA,OAAOC,GAAG,KAAK;YACjDJ,OAAOJ,MAAMI,KAAK;YAClB,mGAAmG;YACnGM,KAAKV,MAAMU,GAAG;QAChB;QACA,sGAAsG;QACtGT,SAAS;YACPQ,KAAKR,QAAQQ,GAAG;QAClB;QACAP,SAAS;YACPE,OAAOF,QAAQE,KAAK;YACpBM,KAAKR,QAAQQ,GAAG;QAClB;IACF;AACF;AAEA,MAAMC,UAAU,AAAC,CAAA;QAEMC,oCASCT;IAVtB,MAAMS,uBAAuBtC;IAC7B,MAAMuC,gBAAeD,qCAAAA,oBAAoB,CAACtB,aAAa,qBAAlCsB,kCAAoC,CAACxB,SAAS;IAEnE,oDAAoD;IACpD,IAAIyB,cAAc;QAChB,OAAOA;IACT;IAEA,yHAAyH;IACzH,qDAAqD;IACrD,IAAIC,mBAAkBX,oCAAAA,4BAAmB,CAACb,aAAa,qBAAjCa,iCAAmC,CAACf,SAAS;IAEnE,IAAI0B,iBAAiB;QACnBlB,KAAImB,IAAI,CACN,CAAC,0CAA0C,EAAED,gBAAgB,0DAA0D,CAAC;IAE5H,OAAO;QACLlB,KAAImB,IAAI,CACN,CAAC,kDAAkD,EAAEzB,aAAa,CAAC,EAAEF,UAAU;IAEnF;IAEA,OAAO,EAAE;AACX,CAAA;AAEA,4EAA4E;AAC5E,yEAAyE;AACzE,gDAAgD;AAChD,kEAAkE;AAClE,EAAE;AACF,yEAAyE;AACzE,MAAM4B,uCACJ/B,QAAQC,GAAG,CAAC8B,oCAAoC;AAElD,SAASC,qBAAqBC,OAAY;IACxC,MAAMC,UAAUD,QAAQC,OAAO;IAE/B,IAAIA,WAAWA,YAAYnC,aAAa;QACtCY,KAAImB,IAAI,CACN,CAAC,yCAAyC,EAAEI,QAAQ,qBAAqB,EAAEnC,YAAY,2BAA2B,CAAC;IAEvH;AACF;AAEA,iEAAiE;AACjE,0EAA0E;AAC1E,2DAA2D;AAC3D,yEAAyE;AACzE,+DAA+D;AAC/D,MAAMoC,kCAAkC;IACtC;IACA;IACA;IACA;IACA;CAGD;AAED,oFAAoF;AACpF,gGAAgG;AAChG,oGAAoG;AACpG,IAAIC,kCAIYC;AAChB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AACJ,IAAIC,gCAA2DN;AAExD,MAAM5C,uBAAgD,CAAC;AAEvD,eAAeD,aACpBoD,gBAAyB,KAAK;IAE9B,2FAA2F;IAC3F,IAAI,CAAC5C,QAAQC,GAAG,CAAC4C,cAAc,EAAE;QAC/B7C,QAAQC,GAAG,CAAC4C,cAAc,GAAG;IAC/B;IAEA,IAAIJ,iBAAiB;QACnB,OAAOA;IACT;IAEA,iIAAiI;IACjI,qDAAqD;IACrD,uFAAuF;IACvF,IAAIzC,QAAQ8C,MAAM,CAACC,OAAO,IAAI,MAAM;QAClC,aAAa;QACb/C,QAAQ8C,MAAM,CAACC,OAAO,CAACC,WAAW,oBAAlChD,QAAQ8C,MAAM,CAACC,OAAO,CAACC,WAAW,MAAlChD,QAAQ8C,MAAM,CAACC,OAAO,EAAe;IACvC;IACA,IAAI/C,QAAQiD,MAAM,CAACF,OAAO,IAAI,MAAM;QAClC,aAAa;QACb/C,QAAQiD,MAAM,CAACF,OAAO,CAACC,WAAW,oBAAlChD,QAAQiD,MAAM,CAACF,OAAO,CAACC,WAAW,MAAlChD,QAAQiD,MAAM,CAACF,OAAO,EAAe;IACvC;IAEAN,kBAAkB,IAAIS,QAAQ,OAAOC,SAASC;QAC5C,IAAI,CAAC3D,qBAAqB4D,GAAG,EAAE;YAC7B,yDAAyD;YACzD,0CAA0C;YAC1C5D,qBAAqB4D,GAAG,GAAGC,IAAAA,8CAAsB,EAACtD,QAAQuD,GAAG,IAAIC,KAAK,CACpEC,QAAQC,KAAK;QAEjB;QAEA,IAAIC,WAAkB,EAAE;QACxB,MAAMC,sBAAsB5D,QAAQC,GAAG,CAAC4D,qBAAqB;QAC7D,MAAMC,sBAAsBpC,QAAQqC,IAAI,CACtC,CAACzC,SACC,CAAC,EAACA,0BAAAA,OAAQ0C,GAAG,KAAI7B,gCAAgC8B,QAAQ,CAAC3C,OAAO0C,GAAG;QAExE,MAAME,iBAAiBlE,QAAQmE,QAAQ,CAACC,YAAY;QACpD,yEAAyE;QACzE,yFAAyF;QACzF,MAAMC,8BACJ,AAAC,CAACT,uBAAuBhB,iBACzBkB,uBACAI;QAEF,IAAI,CAACJ,uBAAuBlB,eAAe;YACzCjC,KAAImB,IAAI,CACN,CAAC,mEAAmE,EAAEzB,aAAa,CAAC,EAAEF,SAAS,qBAAqB,CAAC;QAEzH;QAEA,IAAIkE,6BAA6B;YAC/BjC,kCAAkC;YAClC,MAAMkC,mBAAmB,MAAMC,wBAAwBZ;YACvD,IAAIW,kBAAkB;gBACpB,OAAOnB,QAAQmB;YACjB;QACF;QAEA,4CAA4C;QAC5C,EAAE;QACF,kEAAkE;QAClE,0GAA0G;QAC1G,gHAAgH;QAChH,kHAAkH;QAClH,kDAAkD;QAClD,uDAAuD;QACvD,IAAI;YACF,OAAOnB,QAAQqB;QACjB,EAAE,OAAOC,GAAG;YACV,IACEC,MAAMC,OAAO,CAACF,MACdA,EAAEG,KAAK,CAAC,CAACC,IAAMA,EAAEZ,QAAQ,CAAC,0BAC1B;gBACA,IAAIK,mBAAmB,MAAMQ,0BAA0BnB;gBAEvD,IAAIW,kBAAkB;oBACpB,OAAOnB,QAAQmB;gBACjB;YACF;YAEAX,WAAWA,SAASoB,MAAM,CAACN;QAC7B;QAEA,+EAA+E;QAC/E,IAAI,CAACJ,+BAA+B,CAACT,qBAAqB;YACxD,MAAMU,mBAAmB,MAAMC,wBAAwBZ;YACvD,IAAIW,kBAAkB;gBACpB,OAAOnB,QAAQmB;YACjB;QACF;QAEAU,eAAerB,UAAU;IAC3B;IACA,OAAOlB;AACT;AAEA,eAAeqC,0BAA0BnB,QAAuB;IAC9D,MAAMsB,0BAA0BC,aAAI,CAACC,IAAI,CACvCD,aAAI,CAACE,OAAO,CAACC,QAAQlC,OAAO,CAAC,uBAC7B;IAGF,IAAI,CAACR,+BAA+B;QAClCA,gCAAgC2C,IAAAA,kCAAqB,EACnDvF,aACAkF,yBACAvD,QAAQ6D,GAAG,CAAC,CAACjE,SAAgBA,OAAOkE,eAAe;IAEvD;IACA,MAAM7C;IAEN,IAAI;QACF,OAAO6B,WAAWS;IACpB,EAAE,OAAOR,GAAQ;QACfd,SAAS8B,IAAI,IAAI,EAAE,CAACV,MAAM,CAACN;IAC7B;IAEA,OAAOpC;AACT;AAEA,eAAekC,wBAAwBZ,QAAe;IACpD,IAAI;QACF,IAAI+B,WAAW,MAAMC,SAAS;QAC9B,sDAAsD;QACtDC,IAAAA,mCAAmB,EAAC;YAClBC,MAAM;YACNC,yBAAyB1D;QAC3B;QACA,OAAOsD;IACT,EAAE,OAAOjB,GAAQ;QACfd,SAAS8B,IAAI,IAAI,EAAE,CAACV,MAAM,CAACN;IAC7B;IAEA,IAAI;QACF,2DAA2D;QAC3D,+DAA+D;QAC/D,sEAAsE;QACtE,sDAAsD;QACtD,MAAMsB,gBAAgBb,aAAI,CAACC,IAAI,CAC7BD,aAAI,CAACE,OAAO,CAACC,QAAQlC,OAAO,CAAC,uBAC7B;QAEF,IAAI,CAACX,qBAAqB;YACxBA,sBAAsBwD,IAAAA,4BAAe,EAACjG,aAAagG;QACrD;QACA,MAAMvD;QACN,IAAIkD,WAAW,MAAMC,SAASI;QAC9B,sDAAsD;QACtDH,IAAAA,mCAAmB,EAAC;YAClBC,MAAM;YACNC,yBAAyB1D;QAC3B;QAEA,4CAA4C;QAC5C,sCAAsC;QACtC,KAAK,MAAM6D,WAAWtC,SAAU;YAC9BhD,KAAImB,IAAI,CAACmE;QACX;QACA,OAAOP;IACT,EAAE,OAAOjB,GAAQ;QACfd,SAAS8B,IAAI,IAAI,EAAE,CAACV,MAAM,CAACN;IAC7B;AACF;AAEA,SAASyB;IACP,IAAIvC,WAAkB,EAAE;IACxB,IAAI;QACF,OAAOa;IACT,EAAE,OAAOC,GAAG;QACVd,WAAWA,SAASoB,MAAM,CAACN;IAC7B;IAEA,wDAAwD;IACxD,SAAS;IACT,IAAIlC,cAAc;QAChB,OAAOA;IACT;IAEAyC,eAAerB;IACf,MAAM,qBAAyD,CAAzD,IAAIwC,MAAM,2BAA2B;QAAEC,OAAOzC;IAAS,IAAvD,qBAAA;eAAA;oBAAA;sBAAA;IAAwD;AAChE;AAEA,IAAI0C,qBAAqB;AAEzB,SAASrB,eAAerB,QAAa,EAAE2C,YAAY,KAAK;IACtD,4DAA4D;IAC5D,IAAID,oBAAoB;IACxBA,qBAAqB;IAErB,KAAK,IAAIJ,WAAWtC,SAAU;QAC5BhD,KAAImB,IAAI,CAACmE;IACX;IAEA,sDAAsD;IACtDL,IAAAA,mCAAmB,EAAC;QAClBC,MAAMS,YAAY,WAAWjE;QAC7ByD,yBAAyB1D;IAC3B,GACGmE,IAAI,CAAC,IAAM9G,qBAAqB4D,GAAG,IAAIH,QAAQC,OAAO,IACtDqD,OAAO,CAAC;QACP7F,KAAI+C,KAAK,CACP,CAAC,8BAA8B,EAAErD,aAAa,CAAC,EAAEF,SAAS,yEAAyE,CAAC;QAEtIH,QAAQyG,IAAI,CAAC;IACf;AACJ;AAIO,SAAStH,gBAAgB,EAC9BuH,WAAW,EACXC,mBAAmB,EACnBC,MAAM,EACNC,GAAG,EACHC,OAAO,EACPC,mBAAmB,EACnBC,WAAW,EACXC,kBAAkB,EAInB;IACC,IAAIC,YAAuB;QACzBC,QAAQ,EAAE;QACVC,MAAM,EAAE;QACRC,QAAQ,EAAE;IACZ;IAEA,KAAK,MAAMC,WAAWC,OAAOC,IAAI,CAACN,WAA0C;QAC1EA,SAAS,CAACI,QAAQ,GAAGG,WACnBC,IAAAA,6BAAY,EAAC;YACXhB;YACAC;YACAC;YACAC;YACAC;YACAC;YACAC;YACAW,UAAUL,YAAY;YACtBM,cAAcN,YAAY;YAC1BO,yBAAyBP,YAAY,YAAYA,YAAY;YAC7DQ,cAAcR,YAAY;YAC1BL;QACF;IAEJ;IAEA,OAAOC;AACT;AAEA,SAASO,WAAWxH,GAA2B;IAC7C,OAAOsH,OAAOQ,OAAO,CAAC9H,KACnBoB,MAAM,CAAC,CAAC,CAAC2G,GAAGC,MAAM,GAAKA,SAAS,MAChC1C,GAAG,CAAC,CAAC,CAAC2C,MAAMD,MAAM,GAAM,CAAA;YACvBC;YACAD;QACF,CAAA;AACJ;AAEA,mCAAmC;AACnC,SAASE,aACPC,OAAoB,EACpBC,KAAc;IAMd,MAAMC,SAAS,IAAK,MAAMC,eAAepC;IAAO;IAEhD;;GAEC,GACD,SAASqC,UACPC,KAAY,EACZC,cAAoC;QAEpC,MAAM,qBAAgD,CAAhD,IAAIvC,MAAM,CAAC,WAAW,EAAEuC,eAAeD,QAAQ,GAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAA+C;IACvD;IAEA,eAAeE,eAAkBC,EAAoB;QACnD,IAAI;YACF,OAAO,MAAMA;QACf,EAAE,OAAOC,aAAkB;YACzB,MAAM,IAAIC,6BAAsB,CAACD;QACnC;IACF;IAEA;;;;;GAKC,GACD,SAASE,UACPC,SAAkB,EAClBC,cAEiE;QAKjE,mEAAmE;QACnE,wCAAwC;QACxC,IAAIC,SAAuB,EAAE;QAC7B,sEAAsE;QACtE,qDAAqD;QACrD,IAAIC;QAMJ,IAAIC,WAAW;QAEf,0EAA0E;QAC1E,2EAA2E;QAC3E,2BAA2B;QAC3B,SAASC,WAAWC,GAAsB,EAAErB,KAAoB;YAC9D,IAAIkB,SAAS;gBACX,IAAI,EAAEhG,OAAO,EAAEoG,MAAM,EAAE,GAAGJ;gBAC1BA,UAAU9G;gBACV,IAAIiH,KAAKC,OAAOD;qBACXnG,QAAQ8E;YACf,OAAO;gBACL,MAAMuB,OAAO;oBAAEF;oBAAKrB;gBAAM;gBAC1B,IAAIe,WAAWE,OAAOzD,IAAI,CAAC+D;qBACtBN,MAAM,CAAC,EAAE,GAAGM;YACnB;QACF;QAEA,gBAAgBC;YACd,MAAMC,OAAO,MAAMf,eAAkD,IACnEM,eAAeI;YAEjB,IAAI;gBACF,MAAO,CAACD,SAAU;oBAChB,IAAIF,OAAOS,MAAM,GAAG,GAAG;wBACrB,MAAMH,OAAON,OAAOU,KAAK;wBACzB,IAAIJ,KAAKF,GAAG,EAAE,MAAME,KAAKF,GAAG;wBAC5B,MAAME,KAAKvB,KAAK;oBAClB,OAAO;wBACL,wCAAwC;wBACxC,MAAM,IAAI/E,QAAW,CAACC,SAASoG;4BAC7BJ,UAAU;gCAAEhG;gCAASoG;4BAAO;wBAC9B;oBACF;gBACF;YACF,EAAE,OAAOM,GAAG;gBACV,IAAIA,MAAMvB,QAAQ;gBAClB,IAAIuB,aAAa1D,OAAO;oBACtB,MAAM,IAAI2C,6BAAsB,CAACe;gBACnC;gBACA,MAAMA;YACR,SAAU;gBACR,IAAIH,MAAM;oBACRtB,QAAQ0B,eAAe,CAACJ;gBAC1B;YACF;QACF;QAEA,MAAMK,WAAWN;QACjBM,SAASC,MAAM,GAAG;YAChBZ,WAAW;YACX,IAAID,SAASA,QAAQI,MAAM,CAACjB;YAC5B,OAAO;gBAAEL,OAAO5F;gBAAW4H,MAAM;YAAK;QACxC;QACA,OAAOF;IACT;IAEA,eAAeG,sBACbC,OAAuB;QAEvB,OAAO;YACL,GAAGA,OAAO;YACVC,YAAY,MAAMC,oBAChBF,QAAQC,UAAU,EAClBD,QAAQG,WAAW;YAErBC,UAAUC,KAAKC,SAAS,CAACN,QAAQI,QAAQ;YACzCtK,KAAKwH,WAAW0C,QAAQlK,GAAG;QAC7B;IACF;IAEA,eAAeyK,6BACbP,OAAgC;QAEhC,OAAO;YACL,GAAGA,OAAO;YACVC,YACED,QAAQC,UAAU,IACjB,MAAMC,oBAAoBF,QAAQC,UAAU,EAAED,QAAQG,WAAW;YACpEC,UAAUJ,QAAQI,QAAQ,IAAIC,KAAKC,SAAS,CAACN,QAAQI,QAAQ;YAC7DtK,KAAKkK,QAAQlK,GAAG,IAAIwH,WAAW0C,QAAQlK,GAAG;QAC5C;IACF;IAEA,MAAM0K;QAGJC,YAAYC,aAAwC,CAAE;YACpD,IAAI,CAACC,cAAc,GAAGD;QACxB;QAEA,MAAME,OAAOZ,OAAgC,EAAE;YAC7C,MAAMxB,eAAe,UACnBP,QAAQ4C,aAAa,CACnB,IAAI,CAACF,cAAc,EACnB,MAAMJ,6BAA6BP;QAGzC;QAEAc,uBAAuB;YAqDrB,MAAMC,eAAenC,UACnB,OACA,OAAOoC,WACL/C,QAAQgD,2BAA2B,CAAC,IAAI,CAACN,cAAc,EAAEK;YAE7D,OAAO,AAAC;gBACN,WAAW,MAAME,eAAeH,aAAc;oBAC5C,MAAMI,SAAS,IAAIC;oBACnB,KAAK,MAAM,EAAEC,QAAQ,EAAE,GAAGC,aAAa,IAAIJ,YAAYC,MAAM,CAAE;wBAC7D,IAAII;wBACJ,MAAMC,YAAYF,YAAYG,IAAI;wBAClC,OAAQD;4BACN,KAAK;gCACHD,QAAQ;oCACNE,MAAM;oCACNC,cAAc,IAAIC,aAAaL,YAAYI,YAAY;oCACvDE,cAAc,IAAID,aAAaL,YAAYM,YAAY;gCACzD;gCACA;4BACF,KAAK;gCACHL,QAAQ;oCACNE,MAAM;oCACNI,UAAU,IAAIF,aAAaL,YAAYO,QAAQ;gCACjD;gCACA;4BACF,KAAK;gCACHN,QAAQ;oCACNE,MAAM;oCACNK,OAAOR,YAAYQ,KAAK,CAAC1G,GAAG,CAAC,CAAC2G,OAAU,CAAA;4CACtCC,cAAcD,KAAKC,YAAY;4CAC/BN,cAAc,IAAIC,aAAaI,KAAKL,YAAY;4CAChDO,aAAa,IAAIN,aAAaI,KAAKE,WAAW;wCAChD,CAAA;gCACF;gCACA;4BACF,KAAK;gCACHV,QAAQ;oCACNE,MAAM;oCACNO,cAAcV,YAAYU,YAAY;oCACtCH,UAAU,IAAIF,aAAaL,YAAYO,QAAQ;gCACjD;gCACA;4BACF,KAAK;gCACHN,QAAQ;oCACNE,MAAM;gCACR;gCACA;4BACF;gCACE,MAAMS,mBAA0BV;gCAChCnD,UACEiD,aACA,IAAM,CAAC,oBAAoB,EAAEY,kBAAkB;wBAErD;wBACAf,OAAOgB,GAAG,CAACd,UAAUE;oBACvB;oBACA,MAAMa,6BAA6B,CAACC,aAAgC,CAAA;4BAClER,UAAU,IAAIF,aAAaU,WAAWR,QAAQ;4BAC9CS,SAASD,WAAWC,OAAO;4BAC3BC,SAASF,WAAWE,OAAO;wBAC7B,CAAA;oBACA,MAAMF,aAAanB,YAAYmB,UAAU,GACrCD,2BAA2BlB,YAAYmB,UAAU,IACjDnK;oBACJ,MAAMsK,uCAAuC,CAC3CC,kBACI,CAAA;4BACJC,QAAQ,IAAIf,aAAac,gBAAgBC,MAAM;4BAC/CzF,MAAM,IAAI0E,aAAac,gBAAgBxF,IAAI;wBAC7C,CAAA;oBACA,MAAMwF,kBAAkBvB,YAAYuB,eAAe,GAC/CD,qCAAqCtB,YAAYuB,eAAe,IAChEvK;oBACJ,MAAM;wBACJiJ;wBACAkB;wBACAI;wBACAE,uBAAuB,IAAIhB,aACzBT,YAAYyB,qBAAqB;wBAEnCC,kBAAkB,IAAIjB,aAAaT,YAAY0B,gBAAgB;wBAC/DC,oBAAoB,IAAIlB,aACtBT,YAAY2B,kBAAkB;wBAEhCC,QAAQ5B,YAAY4B,MAAM;wBAC1BC,aAAa7B,YAAY6B,WAAW;oBACtC;gBACF;YACF;QACF;QAEAC,UAAUC,UAAkB,EAAE;YAC5B,OAAOrE,UAAmC,MAAM,OAAOoC,WACrD/C,QAAQiF,gBAAgB,CAAC,IAAI,CAACvC,cAAc,EAAEsC,YAAYjC;QAE9D;QAEAmC,0BAA0B;YACxB,OAAOvE,UACL,OACA,OAAOoC,WACL/C,QAAQmF,8BAA8B,CAAC,IAAI,CAACzC,cAAc,EAAEK;QAElE;QAEAqC,YACEC,UAA+B,EAC/BC,uBAA+B,EACM;YACrC,OAAOtF,QAAQuF,kBAAkB,CAC/B,IAAI,CAAC7C,cAAc,EACnB2C,YACAC;QAEJ;QAEAE,kBAAkBC,QAAgB,EAA0B;YAC1D,OAAOzF,QAAQ0F,wBAAwB,CAAC,IAAI,CAAChD,cAAc,EAAE+C;QAC/D;QAEAE,aAAaF,QAAgB,EAA0B;YACrD,OAAOzF,QAAQ4F,mBAAmB,CAAC,IAAI,CAAClD,cAAc,EAAE+C;QAC1D;QAEAI,iBAAiBJ,QAAgB,EAAiB;YAChD,OAAOzF,QAAQ8F,uBAAuB,CAAC,IAAI,CAACpD,cAAc,EAAE+C;QAC9D;QAEAM,oBAAoBC,aAAqB,EAAE;YACzC,OAAOrF,UAA0C,MAAM,OAAOoC,WAC5D/C,QAAQiG,0BAA0B,CAChC,IAAI,CAACvD,cAAc,EACnBsD,eACAjD;QAGN;QAEAmD,WAA0B;YACxB,OAAOlG,QAAQmG,eAAe,CAAC,IAAI,CAACzD,cAAc;QACpD;QAEA0D,SAAwB;YACtB,OAAOpG,QAAQqG,aAAa,CAAC,IAAI,CAAC3D,cAAc;QAClD;IACF;IAEA,MAAMgB;QAGJlB,YAAY8D,cAA0C,CAAE;YACtD,IAAI,CAACC,eAAe,GAAGD;QACzB;QAEA,MAAME,cAAyD;YAC7D,OAAO,MAAMjG,eACX,IACEP,QAAQyG,mBAAmB,CAAC,IAAI,CAACF,eAAe;QAItD;QAEA,MAAMG,gBAAqE;YACzE,MAAMC,qBAAqBhG,UACzB,OACA,OAAOoC,WACL/C,QAAQ4G,8BAA8B,CACpC,MAAM,IAAI,CAACL,eAAe,EAC1BxD;YAGN,MAAM4D,mBAAmBE,IAAI;YAC7B,OAAOF;QACT;QAEA,MAAMG,cACJC,aAAsB,EAC+B;YACrD,MAAMC,qBAAqBrG,UACzB,OACA,OAAOoC,WACL/C,QAAQiH,8BAA8B,CACpC,MAAM,IAAI,CAACV,eAAe,EAC1BQ,eACAhE;YAGN,MAAMiE,mBAAmBH,IAAI;YAC7B,OAAOG;QACT;IACF;IAEA;;;;;GAKC,GACD,SAASE,kBACPC,kBAAsC,EACtCjF,WAAmB;YAIUF;QAF7B,IAAIA,aAAa;YAAE,GAAImF,kBAAkB;QAAS;QAElD,MAAMC,wBAAuBpF,2BAAAA,WAAWqF,YAAY,qBAAvBrF,yBAAyBsF,aAAa;QAEnE,gHAAgH;QAChH,mFAAmF;QACnF,IAAIF,sBAAsB;gBAGVpF,gCAAAA;YAFd,MAAMuF,WAAW;gBAAC;gBAAQ;gBAAQ;gBAAS;aAAQ;YACnD,IACEpI,OAAOC,IAAI,CAAC4C,CAAAA,+BAAAA,4BAAAA,WAAYqF,YAAY,sBAAxBrF,iCAAAA,0BAA0BwF,KAAK,qBAA/BxF,+BAAiCyF,KAAK,KAAI,EAAE,EAAE9L,IAAI,CAAC,CAAC+L,MAC9DH,SAAS1L,QAAQ,CAAC6L,OAEpB;gBACAnP,KAAImB,IAAI,CACN,CAAC,4PAA4P,CAAC;YAElQ,OAAO;gBACL,IAAI,CAACsI,WAAWqF,YAAY,CAACG,KAAK,EAAE;oBAClCxF,WAAWqF,YAAY,CAACG,KAAK,GAAG,CAAC;gBACnC;gBAEA,IAAI,CAACxF,WAAWqF,YAAY,CAACG,KAAK,CAACC,KAAK,EAAE;oBACxCzF,WAAWqF,YAAY,CAACG,KAAK,CAACC,KAAK,GAAG,CAAC;gBACzC;gBAEA,KAAK,MAAMC,OAAO;oBAAC;oBAAQ;oBAAQ;oBAAS;iBAAQ,CAAE;oBACpD1F,WAAWqF,YAAY,CAACG,KAAK,CAACC,KAAK,CAACC,IAAI,GAAG;wBACzCC,SAAS;4BACPC,SAAS;4BACTC,SAAS;gCACPC,IAAAA,4CAAsB,EACpBX,mBAAmBE,YAAY,CAACC,aAAa,EAC7CpF,aACAF,WAAWvD,GAAG,EACd,OACAxE;6BAEH;wBACH;oBACF;gBACF;YACF;QACF;QAEA,OAAO+H;IACT;IAEA,eAAeC,oBACbD,UAA8B,EAC9BE,WAAmB;YAYf6F,4CAAAA;QAVJ,mDAAmD;QACnD,IAAIA,yBAAyBb,kBAAkBlF,YAAYE;QAE3D6F,uBAAuBC,eAAe,GACpC,OAAMhG,WAAWgG,eAAe,oBAA1BhG,WAAWgG,eAAe,MAA1BhG;QAER,iFAAiF;QACjF+F,uBAAuBE,aAAa,GAAG,CAAC;QACxCF,uBAAuBG,OAAO,GAAGlG,WAAWkG,OAAO,IAAI,CAAC;QAExD,KAAIH,uCAAAA,uBAAuBV,YAAY,sBAAnCU,6CAAAA,qCAAqCP,KAAK,qBAA1CO,2CAA4CN,KAAK,EAAE;gBAEnDM;YADFI,sCACEJ,8CAAAA,uBAAuBV,YAAY,CAACG,KAAK,qBAAzCO,4CAA2CN,KAAK;QAEpD;QAEAM,uBAAuBK,iBAAiB,GACtCL,uBAAuBK,iBAAiB,GACpCjJ,OAAOkJ,WAAW,CAChBlJ,OAAOQ,OAAO,CAAMoI,uBAAuBK,iBAAiB,EAAEjL,GAAG,CAC/D,CAAC,CAACmL,KAAK9J,OAAO,GAAK;gBACjB8J;gBACA;oBACE,GAAG9J,MAAM;oBACT/G,WACE,OAAO+G,OAAO/G,SAAS,KAAK,WACxB+G,OAAO/G,SAAS,GAChB0H,OAAOQ,OAAO,CAACnB,OAAO/G,SAAS,EAAE0F,GAAG,CAAC,CAAC,CAACuK,KAAK7H,MAAM,GAAK;4BACrD6H;4BACA7H;yBACD;gBACT;aACD,KAGL5F;QAEN,2EAA2E;QAC3E,IAAI8N,uBAAuBQ,MAAM,CAACC,UAAU,EAAE;YAC5CT,uBAAuBQ,MAAM,GAAG;gBAC9B,GAAGvG,WAAWuG,MAAM;gBACpBC,YACE,OAAO1L,aAAI,CAAC2L,QAAQ,CAACvG,aAAaF,WAAWuG,MAAM,CAACC,UAAU;YAClE;QACF;QAEA,OAAOpG,KAAKC,SAAS,CAAC0F,wBAAwB,MAAM;IACtD;IAEA,SAASI,qCACPO,cAA6D;QAE7D,KAAK,MAAM,CAACC,MAAMC,KAAK,IAAIzJ,OAAOQ,OAAO,CAAC+I,gBAAiB;YACzD,IAAIpM,MAAMC,OAAO,CAACqM,OAAO;gBACvBC,iBAAiBD,MAAMD;YACzB,OAAO;gBACLG,gBAAgBF,MAAMD;YACxB;QACF;QAEA,SAASG,gBAAgBF,IAAyB,EAAED,IAAY;YAC9D,IAAI,CAACC,MAAM;YACX,IAAI,aAAaA,MAAM;gBACrBC,iBAAiB,AAACD,KAAoCf,OAAO,EAAEc;YACjE,OAAO;gBACL,IAAK,MAAMjB,OAAOkB,KAAM;oBACtB,MAAMG,QAAQH,IAAI,CAAClB,IAAI;oBACvB,IAAI,OAAOqB,UAAU,YAAYA,OAAO;wBACtCD,gBAAgBC,OAAOJ;oBACzB;gBACF;YACF;QACF;QAEA,SAASE,iBAAiBG,WAA8B,EAAEL,IAAY;YACpE,KAAK,MAAMM,cAAcD,YAAa;gBACpC,IACE,OAAOC,eAAe,YACtB,CAACC,IAAAA,uBAAiB,EAACD,YAAY7G,KAAK7K,KAAK,CAAC6K,KAAKC,SAAS,CAAC4G,eACzD;oBACA,MAAM,qBAEL,CAFK,IAAIlL,MACR,CAAC,OAAO,EAAEkL,WAAWE,MAAM,CAAC,YAAY,EAAER,KAAK,yGAAyG,CAAC,GADrJ,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,OAAO,eAAeS,cACpBrH,OAAuB,EACvBsH,kBAAkB;QAElB,OAAO,IAAI9G,YACT,MAAMvC,QAAQsJ,UAAU,CACtB,MAAMxH,sBAAsBC,UAC5BsH,sBAAsB,CAAC;IAG7B;AACF;AAEA,eAAe9L,SAASgM,aAAa,EAAE;IACrC,IAAIpP,cAAc;QAChB,OAAOA;IACT;IAEA,IAAIoB,WAAW,EAAE;IACjB,KAAK,IAAIiO,OAAO;QAAC;QAAyB;KAAqB,CAAE;QAC/D,IAAI;YACF,IAAIC,UAAUD;YAEd,IAAID,YAAY;gBACd,yDAAyD;gBACzDE,UAAU3M,aAAI,CAACC,IAAI,CAACwM,YAAYC,KAAK;YACvC;YACA,IAAIlM,WAA4B,MAAM,MAAM,CAC1CoM,IAAAA,kBAAa,EAACD,SAASE,QAAQ;YAEjC,IAAIH,QAAQ,sBAAsB;gBAChClM,WAAW,MAAMA,SAASsM,OAAO;YACnC;YACAzR,QAAQ;YAER,mEAAmE;YACnE,yCAAyC;YACzCgC,eAAe;gBACb0P,KAAK;oBACHC,WAAW;wBACTrS,WAAW,SAAUsS,QAAa;4BAChC,MAAM,qBAEL,CAFK,IAAIhM,MACR,qEADI,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;wBACAiM,oBAAoB,SAAUD,QAAa;4BACzC,MAAM,qBAEL,CAFK,IAAIhM,MACR,8EADI,qBAAA;uCAAA;4CAAA;8CAAA;4BAEN;wBACF;oBACF;gBACF;gBACA5G,QAAQ;gBACRM,WAAUwS,GAAW,EAAElI,OAAY;oBACjC,oHAAoH;oBACpH,OAAOzE,CAAAA,4BAAAA,SAAU7F,SAAS,IACtB6F,SAAS7F,SAAS,CAACwS,IAAIN,QAAQ,IAAI5H,WACnCjH,QAAQC,OAAO,CAACuC,SAAS5F,aAAa,CAACuS,IAAIN,QAAQ,IAAI5H;gBAC7D;gBACArK,eAAcuS,GAAW,EAAElI,OAAY;oBACrC,OAAOzE,SAAS5F,aAAa,CAACuS,IAAIN,QAAQ,IAAI5H;gBAChD;gBACAzK,QAAO2S,GAAW,EAAElI,OAAY;oBAC9B,OAAOzE,CAAAA,4BAAAA,SAAUhG,MAAM,IACnBgG,SAAShG,MAAM,CAAC2S,IAAIN,QAAQ,IAAI5H,WAChCjH,QAAQC,OAAO,CAACuC,SAAS4M,UAAU,CAACD,IAAIN,QAAQ,IAAI5H;gBAC1D;gBACAmI,YAAWD,GAAW,EAAElI,OAAY;oBAClC,OAAOzE,SAAS4M,UAAU,CAACD,IAAIN,QAAQ,IAAI5H;gBAC7C;gBACAxK,OAAM0S,GAAW,EAAElI,OAAY;oBAC7B,OAAOzE,CAAAA,4BAAAA,SAAU/F,KAAK,IAClB+F,SAAS/F,KAAK,CAAC0S,IAAIN,QAAQ,IAAI5H,WAC/BjH,QAAQC,OAAO,CAACuC,SAAS6M,SAAS,CAACF,IAAIN,QAAQ,IAAI5H;gBACzD;gBACAqI;oBACE,OAAOnQ;gBACT;gBACAuN,OAAO;oBACL4B,eAAe,SACbW,QAAwB,EACxBM,mBAAoD;wBAEpD,MAAM,qBAEL,CAFK,IAAItM,MACR,iEADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBACAuM,2BAA2B,SAAUC,cAAsB;wBACzD,MAAM,qBAEL,CAFK,IAAIxM,MACR,6EADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBACAyM,KAAK;oBACHC,SAAQR,GAAW,EAAElI,OAAY;wBAC/B,OAAOzE,SAASoN,UAAU,CAACT,KAAKU,cAAc5I;oBAChD;oBACA6I,aAAYX,GAAW,EAAElI,OAAY;wBACnC,OAAOzE,SAASuN,cAAc,CAACZ,KAAKU,cAAc5I;oBACpD;gBACF;YACF;YACA,OAAO5H;QACT,EAAE,OAAOsH,GAAQ;YACf,8DAA8D;YAC9D,IAAI8H,YAAY;gBACd,IAAI9H,CAAAA,qBAAAA,EAAGqJ,IAAI,MAAK,wBAAwB;oBACtCvP,SAAS8B,IAAI,CAAC,CAAC,kBAAkB,EAAEmM,IAAI,0BAA0B,CAAC;gBACpE,OAAO;oBACLjO,SAAS8B,IAAI,CACX,CAAC,kBAAkB,EAAEmM,IAAI,yBAAyB,EAAE/H,EAAEsJ,OAAO,IAAItJ,GAAG;gBAExE;YACF;QACF;IACF;IAEA,MAAMlG;AACR;AAEA,SAASa,WAAWmN,UAAmB;IACrC,IAAIrP,gBAAgB;QAClB,OAAOA;IACT;IAEA,MAAM8Q,iBAA8B,CAAC,CAACrR,uCAClCsD,QAAQtD,wCACR;IACJ,IAAI2D,WAAwB0N;IAC5B,IAAIzP,WAAkB,EAAE;IAExB,MAAM0P,uBAAuBrT,QAAQC,GAAG,CAACoT,oBAAoB;IAC7D,KAAK,MAAM/R,UAAUI,QAAS;QAC5B,IAAI2R,sBAAsB;YACxB,IAAI;gBACF,2GAA2G;gBAC3G3N,WAAWL,QACT,GAAGgO,qBAAqB,UAAU,EAAE/R,OAAOkE,eAAe,CAAC,KAAK,CAAC;gBAEnEjF,QACE;gBAEF;YACF,EAAE,OAAOsJ,GAAG,CAAC;QACf,OAAO;YACL,IAAI;gBACFnE,WAAWL,QACT,CAAC,0BAA0B,EAAE/D,OAAOkE,eAAe,CAAC,KAAK,CAAC;gBAE5DjF,QAAQ;gBACR;YACF,EAAE,OAAOsJ,GAAG,CAAC;QACf;IACF;IAEA,IAAI,CAACnE,UAAU;QACb,KAAK,MAAMpE,UAAUI,QAAS;YAC5B,IAAIkQ,MAAMD,aACNzM,aAAI,CAACC,IAAI,CACPwM,YACA,CAAC,UAAU,EAAErQ,OAAOkE,eAAe,EAAE,EACrC,CAAC,SAAS,EAAElE,OAAOkE,eAAe,CAAC,KAAK,CAAC,IAE3C,CAAC,UAAU,EAAElE,OAAOkE,eAAe,EAAE;YACzC,IAAI;gBACFE,WAAWL,QAAQuM;gBACnB,IAAI,CAACD,YAAY;oBACf3P,qBAAqBqD,QAAQ,GAAGuM,IAAI,aAAa,CAAC;gBACpD;gBACA;YACF,EAAE,OAAO/H,GAAQ;gBACf,IAAIA,CAAAA,qBAAAA,EAAGqJ,IAAI,MAAK,oBAAoB;oBAClCvP,SAAS8B,IAAI,CAAC,CAAC,kBAAkB,EAAEmM,IAAI,0BAA0B,CAAC;gBACpE,OAAO;oBACLjO,SAAS8B,IAAI,CACX,CAAC,kBAAkB,EAAEmM,IAAI,yBAAyB,EAAE/H,EAAEsJ,OAAO,IAAItJ,GAAG;gBAExE;gBACAzH,kCAAkCyH,CAAAA,qBAAAA,EAAGqJ,IAAI,KAAI;YAC/C;QACF;IACF;IAEA,IAAIxN,UAAU;QACZpD,iBAAiB;YACf/C,QAAQ;YACRM,WAAUwS,GAAW,EAAElI,OAAY;oBAO7BA;gBANJ,MAAMmJ,WACJ,OAAOjB,QAAQ,eACf,OAAOA,QAAQ,YACf,CAACkB,OAAOC,QAAQ,CAACnB;gBACnBlI,UAAUA,WAAW,CAAC;gBAEtB,IAAIA,4BAAAA,eAAAA,QAASsJ,GAAG,qBAAZtJ,aAAcuJ,MAAM,EAAE;oBACxBvJ,QAAQsJ,GAAG,CAACC,MAAM,CAACC,MAAM,GAAGxJ,QAAQsJ,GAAG,CAACC,MAAM,CAACC,MAAM,IAAI;gBAC3D;gBAEA,OAAOjO,SAAS7F,SAAS,CACvByT,WAAW9I,KAAKC,SAAS,CAAC4H,OAAOA,KACjCiB,UACAM,SAASzJ;YAEb;YAEArK,eAAcuS,GAAW,EAAElI,OAAY;oBAajCA;gBAZJ,IAAI,OAAOkI,QAAQ,aAAa;oBAC9B,MAAM,qBAEL,CAFK,IAAIlM,MACR,qEADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,OAAO,IAAIoN,OAAOC,QAAQ,CAACnB,MAAM;oBAC/B,MAAM,qBAEL,CAFK,IAAIlM,MACR,qEADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAMmN,WAAW,OAAOjB,QAAQ;gBAChClI,UAAUA,WAAW,CAAC;gBAEtB,IAAIA,4BAAAA,eAAAA,QAASsJ,GAAG,qBAAZtJ,aAAcuJ,MAAM,EAAE;oBACxBvJ,QAAQsJ,GAAG,CAACC,MAAM,CAACC,MAAM,GAAGxJ,QAAQsJ,GAAG,CAACC,MAAM,CAACC,MAAM,IAAI;gBAC3D;gBAEA,OAAOjO,SAAS5F,aAAa,CAC3BwT,WAAW9I,KAAKC,SAAS,CAAC4H,OAAOA,KACjCiB,UACAM,SAASzJ;YAEb;YAEAzK,QAAO2S,GAAW,EAAElI,OAAY;gBAC9B,OAAOzE,SAAShG,MAAM,CAACkU,SAASvB,MAAMuB,SAASzJ,WAAW,CAAC;YAC7D;YAEAmI,YAAWD,GAAW,EAAElI,OAAY;gBAClC,OAAOzE,SAAS4M,UAAU,CAACsB,SAASvB,MAAMuB,SAASzJ,WAAW,CAAC;YACjE;YAEAxK,OAAM0S,GAAW,EAAElI,OAAY;gBAC7B,OAAOzE,SAAS/F,KAAK,CAAC0S,KAAKuB,SAASzJ,WAAW,CAAC;YAClD;YAEAqI,iBAAiB9M,SAAS8M,eAAe;YACzClT,2BAA2BoG,SAASpG,yBAAyB;YAC7DM,yBAAyB8F,SAAS9F,uBAAuB;YACzDgQ,OAAO;gBACL4B,eAAerJ,aAAaiL,kBAAkB1N,UAAU;gBACxDgN,2BAA0BmB,aAAa;oBACrClT,KAAImB,IAAI,CACN;oBAEAsR,CAAAA,kBAAkB1N,QAAO,EAAGgN,yBAAyB,CAACmB;gBAC1D;YACF;YACAjB,KAAK;gBACHC,SAAQR,GAAW,EAAElI,OAAY;oBAC/B,OAAOzE,SAASoN,UAAU,CAACT,KAAKuB,SAASb,cAAc5I;gBACzD;gBACA6I,aAAYX,GAAW,EAAElI,OAAY;oBACnCzE,SAASuN,cAAc,CAACZ,KAAKuB,SAASb,cAAc5I;gBACtD;YACF;YACA8H,KAAK;gBACHC,WAAW;oBACTrS,WAAUiU,gBAAqB;wBAC7B,OAAOpO,SAASqO,qBAAqB,CAACD;oBACxC;oBACA1B,oBAAmB4B,oBAAyB;wBAC1C,OAAOtO,SAASuO,mCAAmC,CACjDD;oBAEJ;gBACF;YACF;QACF;QACA,OAAO1R;IACT;IAEA,MAAMqB;AACR;AAEA,2DAA2D;AAC3D,0CAA0C;AAC1C,SAASoP,cAAc5I,UAAe,CAAC,CAAC;IACtC,OAAO;QACL,GAAGA,OAAO;QACV+J,aAAa/J,QAAQ+J,WAAW,IAAI;QACpCC,KAAKhK,QAAQgK,GAAG,IAAI;QACpBC,SAASjK,QAAQiK,OAAO,IAAI;IAC9B;AACF;AAEA,SAASR,SAASS,CAAM;IACtB,OAAOd,OAAOe,IAAI,CAAC9J,KAAKC,SAAS,CAAC4J;AACpC;AAEO,eAAe9U;IACpB,IAAImG,WAAW,MAAMlG;IACrB,OAAOkG,SAASnG,MAAM;AACxB;AAEO,eAAeM,UAAUwS,GAAW,EAAElI,OAAa;IACxD,IAAIzE,WAAW,MAAMlG;IACrB,OAAOkG,SAAS7F,SAAS,CAACwS,KAAKlI;AACjC;AAEO,SAASrK,cAAcuS,GAAW,EAAElI,OAAa;IACtD,IAAIzE,WAAWQ;IACf,OAAOR,SAAS5F,aAAa,CAACuS,KAAKlI;AACrC;AAEO,eAAezK,OACpB2S,GAAW,EACXlI,OAAY;IAEZ,IAAIzE,WAAW,MAAMlG;IACrB,OAAOkG,SAAShG,MAAM,CAAC2S,KAAKlI;AAC9B;AAEO,eAAexK,MAAM0S,GAAW,EAAElI,OAAY;IACnD,IAAIzE,WAAW,MAAMlG;IACrB,IAAI+U,gBAAgBC,IAAAA,yBAAgB,EAACrK;IACrC,OAAOzE,SACJ/F,KAAK,CAAC0S,KAAKkC,eACXhO,IAAI,CAAC,CAACkO,SAAgBjK,KAAK7K,KAAK,CAAC8U;AACtC;AAEO,SAASrV;QASJsG;IARV,IAAIA;IACJ,IAAI;QACFA,WAAWlB;IACb,EAAE,OAAOqF,GAAG;IACV,sEAAsE;IACxE;IAEA,OAAO;QACL6K,MAAM,EAAEhP,6BAAAA,4BAAAA,SAAU8M,eAAe,qBAAzB9M,+BAAAA;IACV;AACF;AAMO,SAASpG,0BAA0BqV,aAAsB;IAC9D,IAAIjS,oBAAoB;QACtB,6CAA6C;QAC7C,IAAIgD,WAAWlB;QACf9B,qBAAqBgD,SAASpG,yBAAyB,oBAAlCoG,SAASpG,yBAAyB,MAAlCoG,UAAqCiP;IAC5D;AACF;AAEA,SAASC,KAAKhM,EAAc;IAC1B,IAAIiM,WAAW;IAEf,OAAO;QACL,IAAI,CAACA,UAAU;YACbA,WAAW;YAEXjM;QACF;IACF;AACF;AAWO,MAAMhJ,0BAA0BgV,KAAK;IAC1C,IAAI;QACF,IAAIlP,WAAWlB;QACf,IAAI9B,oBAAoB;YACtBgD,SAAS9F,uBAAuB,oBAAhC8F,SAAS9F,uBAAuB,MAAhC8F,UAAmChD;QACrC;IACF,EAAE,OAAOmH,GAAG;IACV,sEAAsE;IACxE;AACF"}