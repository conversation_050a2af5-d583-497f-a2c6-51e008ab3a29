{"version": 3, "sources": ["../../../../src/build/babel/plugins/optimize-hook-destructuring.ts"], "sourcesContent": ["import type {\n  NodePath,\n  types as BabelTypes,\n} from 'next/dist/compiled/babel/core'\nimport type { PluginObj } from 'next/dist/compiled/babel/core'\n// matches any hook-like (the default)\nconst isHook = /^use[A-Z]/\n\n// matches only built-in hooks provided by <PERSON>act et al\nconst isBuiltInHook =\n  /^use(Callback|Context|DebugValue|Effect|ImperativeHandle|LayoutEffect|Memo|Reducer|Ref|State)$/\n\nexport default function ({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj<any> {\n  const visitor = {\n    CallExpression(path: NodePath<BabelTypes.CallExpression>, state: any) {\n      const onlyBuiltIns = state.opts.onlyBuiltIns\n\n      // if specified, options.lib is a list of libraries that provide hook functions\n      const libs =\n        state.opts.lib &&\n        (state.opts.lib === true\n          ? ['react', 'preact/hooks']\n          : [].concat(state.opts.lib))\n\n      // skip function calls that are not the init of a variable declaration:\n      if (!t.isVariableDeclarator(path.parent)) return\n\n      // skip function calls where the return value is not Array-destructured:\n      if (!t.isArrayPattern(path.parent.id)) return\n\n      // name of the (hook) function being called:\n      const hookName = (path.node.callee as BabelTypes.Identifier).name\n\n      if (libs) {\n        const binding = path.scope.getBinding(hookName)\n        // not an import\n        if (!binding || binding.kind !== 'module') return\n\n        const specifier = (binding.path.parent as BabelTypes.ImportDeclaration)\n          .source.value\n        // not a match\n        if (!libs.some((lib: any) => lib === specifier)) return\n      }\n\n      // only match function calls with names that look like a hook\n      if (!(onlyBuiltIns ? isBuiltInHook : isHook).test(hookName)) return\n\n      path.parent.id = t.objectPattern(\n        path.parent.id.elements.reduce<Array<BabelTypes.ObjectProperty>>(\n          (patterns, element, i) => {\n            if (element === null) {\n              return patterns\n            }\n\n            return patterns.concat(\n              t.objectProperty(\n                t.numericLiteral(i),\n                // TODO: fix this\n                element as Exclude<\n                  typeof element,\n                  BabelTypes.MemberExpression | BabelTypes.TSParameterProperty\n                >\n              )\n            )\n          },\n          []\n        )\n      )\n    },\n  }\n\n  return {\n    name: 'optimize-hook-destructuring',\n    visitor: {\n      // this is a workaround to run before preset-env destroys destructured assignments\n      Program(path, state) {\n        path.traverse(visitor, state)\n      },\n    },\n  }\n}\n"], "names": ["isHook", "isBuiltInHook", "types", "t", "visitor", "CallExpression", "path", "state", "onlyBuiltIns", "opts", "libs", "lib", "concat", "isVariableDeclarator", "parent", "isArrayPattern", "id", "<PERSON><PERSON><PERSON>", "node", "callee", "name", "binding", "scope", "getBinding", "kind", "specifier", "source", "value", "some", "test", "objectPattern", "elements", "reduce", "patterns", "element", "i", "objectProperty", "numericLiteral", "Program", "traverse"], "mappings": ";;;;+BAYA;;;eAAA;;;AAPA,sCAAsC;AACtC,MAAMA,SAAS;AAEf,sDAAsD;AACtD,MAAMC,gBACJ;AAEa,SAAf,SAAyB,EACvBC,OAAOC,CAAC,EAGT;IACC,MAAMC,UAAU;QACdC,gBAAeC,IAAyC,EAAEC,KAAU;YAClE,MAAMC,eAAeD,MAAME,IAAI,CAACD,YAAY;YAE5C,+EAA+E;YAC/E,MAAME,OACJH,MAAME,IAAI,CAACE,GAAG,IACbJ,CAAAA,MAAME,IAAI,CAACE,GAAG,KAAK,OAChB;gBAAC;gBAAS;aAAe,GACzB,EAAE,CAACC,MAAM,CAACL,MAAME,IAAI,CAACE,GAAG,CAAA;YAE9B,uEAAuE;YACvE,IAAI,CAACR,EAAEU,oBAAoB,CAACP,KAAKQ,MAAM,GAAG;YAE1C,wEAAwE;YACxE,IAAI,CAACX,EAAEY,cAAc,CAACT,KAAKQ,MAAM,CAACE,EAAE,GAAG;YAEvC,4CAA4C;YAC5C,MAAMC,WAAW,AAACX,KAAKY,IAAI,CAACC,MAAM,CAA2BC,IAAI;YAEjE,IAAIV,MAAM;gBACR,MAAMW,UAAUf,KAAKgB,KAAK,CAACC,UAAU,CAACN;gBACtC,gBAAgB;gBAChB,IAAI,CAACI,WAAWA,QAAQG,IAAI,KAAK,UAAU;gBAE3C,MAAMC,YAAY,AAACJ,QAAQf,IAAI,CAACQ,MAAM,CACnCY,MAAM,CAACC,KAAK;gBACf,cAAc;gBACd,IAAI,CAACjB,KAAKkB,IAAI,CAAC,CAACjB,MAAaA,QAAQc,YAAY;YACnD;YAEA,6DAA6D;YAC7D,IAAI,CAAC,AAACjB,CAAAA,eAAeP,gBAAgBD,MAAK,EAAG6B,IAAI,CAACZ,WAAW;YAE7DX,KAAKQ,MAAM,CAACE,EAAE,GAAGb,EAAE2B,aAAa,CAC9BxB,KAAKQ,MAAM,CAACE,EAAE,CAACe,QAAQ,CAACC,MAAM,CAC5B,CAACC,UAAUC,SAASC;gBAClB,IAAID,YAAY,MAAM;oBACpB,OAAOD;gBACT;gBAEA,OAAOA,SAASrB,MAAM,CACpBT,EAAEiC,cAAc,CACdjC,EAAEkC,cAAc,CAACF,IACjB,iBAAiB;gBACjBD;YAMN,GACA,EAAE;QAGR;IACF;IAEA,OAAO;QACLd,MAAM;QACNhB,SAAS;YACP,kFAAkF;YAClFkC,SAAQhC,IAAI,EAAEC,KAAK;gBACjBD,KAAKiC,QAAQ,CAACnC,SAASG;YACzB;QACF;IACF;AACF"}