{"version": 3, "sources": ["../../../../../src/build/webpack/plugins/telemetry-plugin/telemetry-plugin.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { NormalModule } from 'next/dist/compiled/webpack/webpack'\nimport {\n  createUseCacheTracker,\n  type UseCacheTrackerKey,\n} from './use-cache-tracker-utils'\n\n/**\n * List of target triples next-swc native binary supports.\n */\nexport type SWC_TARGET_TRIPLE =\n  | 'x86_64-apple-darwin'\n  | 'x86_64-unknown-linux-gnu'\n  | 'x86_64-pc-windows-msvc'\n  | 'i686-pc-windows-msvc'\n  | 'aarch64-unknown-linux-gnu'\n  | 'armv7-unknown-linux-gnueabihf'\n  | 'aarch64-apple-darwin'\n  | 'aarch64-linux-android'\n  | 'arm-linux-androideabi'\n  | 'x86_64-unknown-freebsd'\n  | 'x86_64-unknown-linux-musl'\n  | 'aarch64-unknown-linux-musl'\n  | 'aarch64-pc-windows-msvc'\n\nexport type Feature =\n  | 'next/image'\n  | 'next/future/image'\n  | 'next/legacy/image'\n  | 'next/script'\n  | 'next/dynamic'\n  | '@next/font/google'\n  | '@next/font/local'\n  | 'next/font/google'\n  | 'next/font/local'\n  | 'swcLoader'\n  | 'swcRelay'\n  | 'swcStyledComponents'\n  | 'swcReactRemoveProperties'\n  | 'swcExperimentalDecorators'\n  | 'swcRemoveConsole'\n  | 'swcImportSource'\n  | 'swcEmotion'\n  | `swc/target/${SWC_TARGET_TRIPLE}`\n  | 'turbotrace'\n  | 'transpilePackages'\n  | 'skipMiddlewareUrlNormalize'\n  | 'skipTrailingSlashRedirect'\n  | 'modularizeImports'\n  | 'esmExternals'\n  | 'webpackPlugins'\n  | UseCacheTrackerKey\ninterface FeatureUsage {\n  featureName: Feature\n  invocationCount: number\n}\n\n/**\n * An edge in the module graph.\n */\ninterface Connection {\n  originModule: unknown\n}\n\n// Map of a feature module to the file it belongs in the next package.\nconst FEATURE_MODULE_MAP: ReadonlyMap<Feature, string> = new Map([\n  ['next/image', '/next/image.js'],\n  ['next/future/image', '/next/future/image.js'],\n  ['next/legacy/image', '/next/legacy/image.js'],\n  ['next/script', '/next/script.js'],\n  ['next/dynamic', '/next/dynamic.js'],\n])\nconst FEATURE_MODULE_REGEXP_MAP: ReadonlyMap<Feature, RegExp> = new Map([\n  ['@next/font/google', /\\/@next\\/font\\/google\\/target.css?.+$/],\n  ['@next/font/local', /\\/@next\\/font\\/local\\/target.css?.+$/],\n  ['next/font/google', /\\/next\\/font\\/google\\/target.css?.+$/],\n  ['next/font/local', /\\/next\\/font\\/local\\/target.css?.+$/],\n])\n\n// List of build features used in webpack configuration\nconst BUILD_FEATURES: Array<Feature> = [\n  'swcLoader',\n  'swcRelay',\n  'swcStyledComponents',\n  'swcReactRemoveProperties',\n  'swcExperimentalDecorators',\n  'swcRemoveConsole',\n  'swcImportSource',\n  'swcEmotion',\n  'swc/target/x86_64-apple-darwin',\n  'swc/target/x86_64-unknown-linux-gnu',\n  'swc/target/x86_64-pc-windows-msvc',\n  'swc/target/i686-pc-windows-msvc',\n  'swc/target/aarch64-unknown-linux-gnu',\n  'swc/target/armv7-unknown-linux-gnueabihf',\n  'swc/target/aarch64-apple-darwin',\n  'swc/target/aarch64-linux-android',\n  'swc/target/arm-linux-androideabi',\n  'swc/target/x86_64-unknown-freebsd',\n  'swc/target/x86_64-unknown-linux-musl',\n  'swc/target/aarch64-unknown-linux-musl',\n  'swc/target/aarch64-pc-windows-msvc',\n  'turbotrace',\n  'transpilePackages',\n  'skipMiddlewareUrlNormalize',\n  'skipTrailingSlashRedirect',\n  'modularizeImports',\n  'esmExternals',\n  'webpackPlugins',\n]\n\nexport type TelemetryLoaderContext = {\n  eliminatedPackages?: Set<string>\n  useCacheTracker?: Map<UseCacheTrackerKey, number>\n}\n\nconst eliminatedPackages = new Set<string>()\n\nconst useCacheTracker = createUseCacheTracker()\n\n/**\n * Determine if there is a feature of interest in the specified 'module'.\n */\nfunction findFeatureInModule(module: webpack.Module): Feature | undefined {\n  if (module.type !== 'javascript/auto') {\n    return\n  }\n\n  for (const [feature, path] of FEATURE_MODULE_MAP) {\n    if ((module as webpack.NormalModule).resource.endsWith(path)) {\n      return feature\n    }\n  }\n  const normalizedIdentifier = module.identifier().replace(/\\\\/g, '/')\n  for (const [feature, regexp] of FEATURE_MODULE_REGEXP_MAP) {\n    if (regexp.test(normalizedIdentifier)) {\n      return feature\n    }\n  }\n}\n\n/**\n * Find unique origin modules in the specified 'connections', which possibly\n * contains more than one connection for a module due to different types of\n * dependency.\n */\nfunction findUniqueOriginModulesInConnections(\n  connections: Connection[],\n  originModule: webpack.Module\n): Set<unknown> {\n  const originModules = new Set()\n  for (const connection of connections) {\n    if (\n      !originModules.has(connection.originModule) &&\n      connection.originModule !== originModule\n    ) {\n      originModules.add(connection.originModule)\n    }\n  }\n  return originModules\n}\n\n/**\n * Plugin that queries the ModuleGraph to look for modules that correspond to\n * certain features (e.g. next/image and next/script) and record how many times\n * they are imported.\n */\nexport class TelemetryPlugin implements webpack.WebpackPluginInstance {\n  private usageTracker: Map<Feature, FeatureUsage> = new Map<\n    Feature,\n    FeatureUsage\n  >()\n\n  // Build feature usage is on/off and is known before the build starts\n  constructor(buildFeaturesMap: Map<Feature, boolean>) {\n    for (const featureName of BUILD_FEATURES) {\n      this.usageTracker.set(featureName, {\n        featureName,\n        invocationCount: buildFeaturesMap.get(featureName) ? 1 : 0,\n      })\n    }\n\n    for (const featureName of FEATURE_MODULE_MAP.keys()) {\n      this.usageTracker.set(featureName, {\n        featureName,\n        invocationCount: 0,\n      })\n    }\n\n    for (const featureName of FEATURE_MODULE_REGEXP_MAP.keys()) {\n      this.usageTracker.set(featureName, {\n        featureName,\n        invocationCount: 0,\n      })\n    }\n  }\n\n  public addUsage(\n    featureName: Feature,\n    invocationCount: FeatureUsage['invocationCount']\n  ): void {\n    this.usageTracker.set(featureName, {\n      featureName,\n      invocationCount,\n    })\n  }\n\n  public apply(compiler: webpack.Compiler): void {\n    compiler.hooks.make.tapAsync(\n      TelemetryPlugin.name,\n      async (compilation: webpack.Compilation, callback: () => void) => {\n        compilation.hooks.finishModules.tapAsync(\n          TelemetryPlugin.name,\n          async (modules, modulesFinish) => {\n            for (const module of modules) {\n              const feature = findFeatureInModule(module)\n              if (!feature) {\n                continue\n              }\n              const connections = (\n                compilation as any\n              ).moduleGraph.getIncomingConnections(module)\n              const originModules = findUniqueOriginModulesInConnections(\n                connections,\n                module\n              )\n              this.usageTracker.get(feature)!.invocationCount =\n                originModules.size\n            }\n            modulesFinish()\n          }\n        )\n        callback()\n      }\n    )\n\n    if (compiler.options.mode === 'production' && !compiler.watchMode) {\n      compiler.hooks.thisCompilation.tap(\n        TelemetryPlugin.name,\n        (compilation) => {\n          const moduleHooks = NormalModule.getCompilationHooks(compilation)\n          moduleHooks.loader.tap(TelemetryPlugin.name, (loaderContext) => {\n            ;(loaderContext as TelemetryLoaderContext).eliminatedPackages =\n              eliminatedPackages\n            ;(loaderContext as TelemetryLoaderContext).useCacheTracker =\n              useCacheTracker\n          })\n        }\n      )\n    }\n  }\n\n  public usages(): FeatureUsage[] {\n    return [...this.usageTracker.values()]\n  }\n\n  public packagesUsedInServerSideProps(): string[] {\n    return Array.from(eliminatedPackages)\n  }\n\n  public getUseCacheTracker(): Record<UseCacheTrackerKey, number> {\n    return Object.fromEntries(useCacheTracker)\n  }\n}\n\nexport type TelemetryPluginState = {\n  usages: ReturnType<TelemetryPlugin['usages']>\n  packagesUsedInServerSideProps: ReturnType<\n    TelemetryPlugin['packagesUsedInServerSideProps']\n  >\n  useCacheTracker: ReturnType<TelemetryPlugin['getUseCacheTracker']>\n}\n"], "names": ["TelemetryPlugin", "FEATURE_MODULE_MAP", "Map", "FEATURE_MODULE_REGEXP_MAP", "BUILD_FEATURES", "eliminatedPackages", "Set", "useCacheTracker", "createUseCacheTracker", "findFeatureInModule", "module", "type", "feature", "path", "resource", "endsWith", "normalizedIdentifier", "identifier", "replace", "regexp", "test", "findUniqueOriginModulesInConnections", "connections", "originModule", "originModules", "connection", "has", "add", "constructor", "buildFeaturesMap", "usageTracker", "featureName", "set", "invocationCount", "get", "keys", "addUsage", "apply", "compiler", "hooks", "make", "tapAsync", "name", "compilation", "callback", "finishModules", "modules", "modulesFinish", "moduleGraph", "getIncomingConnections", "size", "options", "mode", "watchMode", "thisCompilation", "tap", "moduleHooks", "NormalModule", "getCompilationHooks", "loader", "loaderContext", "usages", "values", "packagesUsedInServerSideProps", "Array", "from", "getUseCacheTracker", "Object", "fromEntries"], "mappings": ";;;;+BAuKaA;;;eAAAA;;;yBAtKgB;sCAItB;AA2DP,sEAAsE;AACtE,MAAMC,qBAAmD,IAAIC,IAAI;IAC/D;QAAC;QAAc;KAAiB;IAChC;QAAC;QAAqB;KAAwB;IAC9C;QAAC;QAAqB;KAAwB;IAC9C;QAAC;QAAe;KAAkB;IAClC;QAAC;QAAgB;KAAmB;CACrC;AACD,MAAMC,4BAA0D,IAAID,IAAI;IACtE;QAAC;QAAqB;KAAwC;IAC9D;QAAC;QAAoB;KAAuC;IAC5D;QAAC;QAAoB;KAAuC;IAC5D;QAAC;QAAmB;KAAsC;CAC3D;AAED,uDAAuD;AACvD,MAAME,iBAAiC;IACrC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAOD,MAAMC,qBAAqB,IAAIC;AAE/B,MAAMC,kBAAkBC,IAAAA,2CAAqB;AAE7C;;CAEC,GACD,SAASC,oBAAoBC,MAAsB;IACjD,IAAIA,OAAOC,IAAI,KAAK,mBAAmB;QACrC;IACF;IAEA,KAAK,MAAM,CAACC,SAASC,KAAK,IAAIZ,mBAAoB;QAChD,IAAI,AAACS,OAAgCI,QAAQ,CAACC,QAAQ,CAACF,OAAO;YAC5D,OAAOD;QACT;IACF;IACA,MAAMI,uBAAuBN,OAAOO,UAAU,GAAGC,OAAO,CAAC,OAAO;IAChE,KAAK,MAAM,CAACN,SAASO,OAAO,IAAIhB,0BAA2B;QACzD,IAAIgB,OAAOC,IAAI,CAACJ,uBAAuB;YACrC,OAAOJ;QACT;IACF;AACF;AAEA;;;;CAIC,GACD,SAASS,qCACPC,WAAyB,EACzBC,YAA4B;IAE5B,MAAMC,gBAAgB,IAAIlB;IAC1B,KAAK,MAAMmB,cAAcH,YAAa;QACpC,IACE,CAACE,cAAcE,GAAG,CAACD,WAAWF,YAAY,KAC1CE,WAAWF,YAAY,KAAKA,cAC5B;YACAC,cAAcG,GAAG,CAACF,WAAWF,YAAY;QAC3C;IACF;IACA,OAAOC;AACT;AAOO,MAAMxB;IAMX,qEAAqE;IACrE4B,YAAYC,gBAAuC,CAAE;aAN7CC,eAA2C,IAAI5B;QAOrD,KAAK,MAAM6B,eAAe3B,eAAgB;YACxC,IAAI,CAAC0B,YAAY,CAACE,GAAG,CAACD,aAAa;gBACjCA;gBACAE,iBAAiBJ,iBAAiBK,GAAG,CAACH,eAAe,IAAI;YAC3D;QACF;QAEA,KAAK,MAAMA,eAAe9B,mBAAmBkC,IAAI,GAAI;YACnD,IAAI,CAACL,YAAY,CAACE,GAAG,CAACD,aAAa;gBACjCA;gBACAE,iBAAiB;YACnB;QACF;QAEA,KAAK,MAAMF,eAAe5B,0BAA0BgC,IAAI,GAAI;YAC1D,IAAI,CAACL,YAAY,CAACE,GAAG,CAACD,aAAa;gBACjCA;gBACAE,iBAAiB;YACnB;QACF;IACF;IAEOG,SACLL,WAAoB,EACpBE,eAAgD,EAC1C;QACN,IAAI,CAACH,YAAY,CAACE,GAAG,CAACD,aAAa;YACjCA;YACAE;QACF;IACF;IAEOI,MAAMC,QAA0B,EAAQ;QAC7CA,SAASC,KAAK,CAACC,IAAI,CAACC,QAAQ,CAC1BzC,gBAAgB0C,IAAI,EACpB,OAAOC,aAAkCC;YACvCD,YAAYJ,KAAK,CAACM,aAAa,CAACJ,QAAQ,CACtCzC,gBAAgB0C,IAAI,EACpB,OAAOI,SAASC;gBACd,KAAK,MAAMrC,UAAUoC,QAAS;oBAC5B,MAAMlC,UAAUH,oBAAoBC;oBACpC,IAAI,CAACE,SAAS;wBACZ;oBACF;oBACA,MAAMU,cAAc,AAClBqB,YACAK,WAAW,CAACC,sBAAsB,CAACvC;oBACrC,MAAMc,gBAAgBH,qCACpBC,aACAZ;oBAEF,IAAI,CAACoB,YAAY,CAACI,GAAG,CAACtB,SAAUqB,eAAe,GAC7CT,cAAc0B,IAAI;gBACtB;gBACAH;YACF;YAEFH;QACF;QAGF,IAAIN,SAASa,OAAO,CAACC,IAAI,KAAK,gBAAgB,CAACd,SAASe,SAAS,EAAE;YACjEf,SAASC,KAAK,CAACe,eAAe,CAACC,GAAG,CAChCvD,gBAAgB0C,IAAI,EACpB,CAACC;gBACC,MAAMa,cAAcC,qBAAY,CAACC,mBAAmB,CAACf;gBACrDa,YAAYG,MAAM,CAACJ,GAAG,CAACvD,gBAAgB0C,IAAI,EAAE,CAACkB;;oBAC1CA,cAAyCvD,kBAAkB,GAC3DA;oBACAuD,cAAyCrD,eAAe,GACxDA;gBACJ;YACF;QAEJ;IACF;IAEOsD,SAAyB;QAC9B,OAAO;eAAI,IAAI,CAAC/B,YAAY,CAACgC,MAAM;SAAG;IACxC;IAEOC,gCAA0C;QAC/C,OAAOC,MAAMC,IAAI,CAAC5D;IACpB;IAEO6D,qBAAyD;QAC9D,OAAOC,OAAOC,WAAW,CAAC7D;IAC5B;AACF"}