{"version": 3, "sources": ["../../../../src/build/webpack/plugins/next-trace-entrypoints-plugin.ts"], "sourcesContent": ["import nodePath from 'path'\nimport type { Span } from '../../../trace'\nimport isError from '../../../lib/is-error'\nimport { nodeFileTrace } from 'next/dist/compiled/@vercel/nft'\nimport type { NodeFileTraceReasons } from 'next/dist/compiled/@vercel/nft'\nimport {\n  CLIENT_REFERENCE_MANIFEST,\n  TRACE_OUTPUT_VERSION,\n  type CompilerNameValues,\n} from '../../../shared/lib/constants'\nimport { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport {\n  NODE_ESM_RESOLVE_OPTIONS,\n  NODE_RESOLVE_OPTIONS,\n} from '../../webpack-config'\nimport type { NextConfigComplete } from '../../../server/config-shared'\nimport picomatch from 'next/dist/compiled/picomatch'\nimport { getModuleBuildInfo } from '../loaders/get-module-build-info'\nimport { getPageFilePath } from '../../entries'\nimport { resolveExternal } from '../../handle-externals'\nimport { isStaticMetadataRoute } from '../../../lib/metadata/is-metadata-route'\nimport { getCompilationSpan } from '../utils'\n\nconst PLUGIN_NAME = 'TraceEntryPointsPlugin'\nexport const TRACE_IGNORES = [\n  '**/*/next/dist/server/next.js',\n  '**/*/next/dist/bin/next',\n]\n\nconst NOT_TRACEABLE = [\n  '.wasm',\n  '.png',\n  '.jpg',\n  '.jpeg',\n  '.gif',\n  '.webp',\n  '.avif',\n  '.ico',\n  '.bmp',\n  '.svg',\n]\n\nfunction getModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string; request?: string } {\n  return compilation.moduleGraph.getModule(dep)\n}\n\nexport function getFilesMapFromReasons(\n  fileList: Set<string>,\n  reasons: NodeFileTraceReasons,\n  ignoreFn?: (file: string, parent?: string) => Boolean\n) {\n  // this uses the reasons tree to collect files specific to a\n  // certain parent allowing us to not have to trace each parent\n  // separately\n  const parentFilesMap = new Map<string, Map<string, { ignored: boolean }>>()\n\n  function propagateToParents(\n    parents: Set<string>,\n    file: string,\n    seen = new Set<string>()\n  ) {\n    for (const parent of parents || []) {\n      if (!seen.has(parent)) {\n        seen.add(parent)\n        let parentFiles = parentFilesMap.get(parent)\n\n        if (!parentFiles) {\n          parentFiles = new Map()\n          parentFilesMap.set(parent, parentFiles)\n        }\n        const ignored = Boolean(ignoreFn?.(file, parent))\n        parentFiles.set(file, { ignored })\n\n        const parentReason = reasons.get(parent)\n\n        if (parentReason?.parents) {\n          propagateToParents(parentReason.parents, file, seen)\n        }\n      }\n    }\n  }\n\n  for (const file of fileList!) {\n    const reason = reasons!.get(file)\n    const isInitial =\n      reason?.type.length === 1 && reason.type.includes('initial')\n\n    if (\n      !reason ||\n      !reason.parents ||\n      (isInitial && reason.parents.size === 0)\n    ) {\n      continue\n    }\n    propagateToParents(reason.parents, file)\n  }\n  return parentFilesMap\n}\n\nexport interface TurbotraceAction {\n  action: 'print' | 'annotate'\n  input: string[]\n  contextDirectory: string\n  processCwd: string\n  showAll?: boolean\n  memoryLimit?: number\n}\n\nexport interface BuildTraceContext {\n  entriesTrace?: {\n    action: TurbotraceAction\n    appDir: string\n    outputPath: string\n    depModArray: string[]\n    entryNameMap: Record<string, string>\n    absolutePathByEntryName: Record<string, string>\n  }\n  chunksTrace?: {\n    action: TurbotraceAction\n    outputPath: string\n    entryNameFilesMap: Record<string, Array<string>>\n  }\n}\n\nexport class TraceEntryPointsPlugin implements webpack.WebpackPluginInstance {\n  public buildTraceContext: BuildTraceContext = {}\n\n  private rootDir: string\n  private appDir: string | undefined\n  private pagesDir: string | undefined\n  private appDirEnabled?: boolean\n  private tracingRoot: string\n  private entryTraces: Map<string, Map<string, { bundled: boolean }>>\n  private traceIgnores: string[]\n  private esmExternals?: NextConfigComplete['experimental']['esmExternals']\n  private compilerType: CompilerNameValues\n\n  constructor({\n    rootDir,\n    appDir,\n    pagesDir,\n    compilerType,\n    appDirEnabled,\n    traceIgnores,\n    esmExternals,\n    outputFileTracingRoot,\n  }: {\n    rootDir: string\n    compilerType: CompilerNameValues\n    appDir: string | undefined\n    pagesDir: string | undefined\n    appDirEnabled?: boolean\n    traceIgnores?: string[]\n    outputFileTracingRoot?: string\n    esmExternals?: NextConfigComplete['experimental']['esmExternals']\n  }) {\n    this.rootDir = rootDir\n    this.appDir = appDir\n    this.pagesDir = pagesDir\n    this.entryTraces = new Map()\n    this.esmExternals = esmExternals\n    this.appDirEnabled = appDirEnabled\n    this.traceIgnores = traceIgnores || []\n    this.tracingRoot = outputFileTracingRoot || rootDir\n    this.compilerType = compilerType\n  }\n\n  // Here we output all traced assets and webpack chunks to a\n  // ${page}.js.nft.json file\n  async createTraceAssets(compilation: webpack.Compilation, span: Span) {\n    const outputPath = compilation.outputOptions.path || ''\n\n    await span.traceChild('create-trace-assets').traceAsyncFn(async () => {\n      const entryFilesMap = new Map<any, Set<string>>()\n      const chunksToTrace = new Set<string>()\n      const entryNameFilesMap = new Map<string, Array<string>>()\n\n      const isTraceable = (file: string) =>\n        !NOT_TRACEABLE.some((suffix) => {\n          return file.endsWith(suffix)\n        })\n\n      for (const entrypoint of compilation.entrypoints.values()) {\n        const entryFiles = new Set<string>()\n\n        for (const chunk of process.env.NEXT_RSPACK\n          ? entrypoint.chunks\n          : entrypoint.getEntrypointChunk().getAllReferencedChunks()) {\n          for (const file of chunk.files) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n          for (const file of chunk.auxiliaryFiles) {\n            if (isTraceable(file)) {\n              const filePath = nodePath.join(outputPath, file)\n              chunksToTrace.add(filePath)\n              entryFiles.add(filePath)\n            }\n          }\n        }\n        entryFilesMap.set(entrypoint, entryFiles)\n        entryNameFilesMap.set(entrypoint.name || '', [...entryFiles])\n      }\n\n      // startTrace existed and callable\n      this.buildTraceContext.chunksTrace = {\n        action: {\n          action: 'annotate',\n          input: [...chunksToTrace],\n          contextDirectory: this.tracingRoot,\n          processCwd: this.rootDir,\n        },\n        outputPath,\n        entryNameFilesMap: Object.fromEntries(entryNameFilesMap),\n      }\n\n      // server compiler outputs to `server/chunks` so we traverse up\n      // one, but edge-server does not so don't for that one\n      const outputPrefix = this.compilerType === 'server' ? '../' : ''\n\n      for (const [entrypoint, entryFiles] of entryFilesMap) {\n        const traceOutputName = `${outputPrefix}${entrypoint.name}.js.nft.json`\n        const traceOutputPath = nodePath.dirname(\n          nodePath.join(outputPath, traceOutputName)\n        )\n\n        // don't include the entry itself in the trace\n        entryFiles.delete(\n          nodePath.join(outputPath, `${outputPrefix}${entrypoint.name}.js`)\n        )\n\n        if (entrypoint.name.startsWith('app/') && this.appDir) {\n          const appDirRelativeEntryPath =\n            this.buildTraceContext.entriesTrace?.absolutePathByEntryName[\n              entrypoint.name\n            ]?.replace(this.appDir, '')\n\n          const entryIsStaticMetadataRoute =\n            appDirRelativeEntryPath &&\n            isStaticMetadataRoute(appDirRelativeEntryPath)\n\n          // Include the client reference manifest in the trace, but not for\n          // static metadata routes, for which we don't generate those.\n          if (!entryIsStaticMetadataRoute) {\n            entryFiles.add(\n              nodePath.join(\n                outputPath,\n                outputPrefix,\n                entrypoint.name.replace(/%5F/g, '_') +\n                  '_' +\n                  CLIENT_REFERENCE_MANIFEST +\n                  '.js'\n              )\n            )\n          }\n        }\n\n        const finalFiles: string[] = []\n\n        await Promise.all(\n          [\n            ...new Set([\n              ...entryFiles,\n              ...(this.entryTraces.get(entrypoint.name)?.keys() || []),\n            ]),\n          ].map(async (file) => {\n            const fileInfo = this.entryTraces.get(entrypoint.name)?.get(file)\n\n            const relativeFile = nodePath\n              .relative(traceOutputPath, file)\n              .replace(/\\\\/g, '/')\n\n            if (file) {\n              if (!fileInfo?.bundled) {\n                finalFiles.push(relativeFile)\n              }\n            }\n          })\n        )\n\n        compilation.emitAsset(\n          traceOutputName,\n          new sources.RawSource(\n            JSON.stringify({\n              version: TRACE_OUTPUT_VERSION,\n              files: finalFiles,\n            })\n          ) as unknown as webpack.sources.RawSource\n        )\n      }\n    })\n  }\n\n  tapfinishModules(\n    compilation: webpack.Compilation,\n    traceEntrypointsPluginSpan: Span,\n    doResolve: (\n      request: string,\n      parent: string,\n      job: import('@vercel/nft/out/node-file-trace').Job,\n      isEsmRequested: boolean\n    ) => Promise<string>,\n    readlink: any,\n    stat: any\n  ) {\n    compilation.hooks.finishModules.tapAsync(\n      PLUGIN_NAME,\n      async (_stats: any, callback: any) => {\n        const finishModulesSpan =\n          traceEntrypointsPluginSpan.traceChild('finish-modules')\n        await finishModulesSpan\n          .traceAsyncFn(async () => {\n            // we create entry -> module maps so that we can\n            // look them up faster instead of having to iterate\n            // over the compilation modules list\n            const entryNameMap = new Map<string, string>()\n            const entryModMap = new Map<string, any>()\n            const additionalEntries = new Map<string, Map<string, any>>()\n            const absolutePathByEntryName = new Map<string, string>()\n\n            const depModMap = new Map<string, any>()\n\n            await finishModulesSpan\n              .traceChild('get-entries')\n              .traceAsyncFn(async () => {\n                for (const [name, entry] of compilation.entries.entries()) {\n                  const normalizedName = name?.replace(/\\\\/g, '/')\n\n                  const isPage = normalizedName.startsWith('pages/')\n                  const isApp =\n                    this.appDirEnabled && normalizedName.startsWith('app/')\n\n                  if (isApp || isPage) {\n                    for (const dep of entry.dependencies) {\n                      if (!dep) continue\n                      const entryMod = getModuleFromDependency(compilation, dep)\n\n                      // Handle case where entry is a loader coming from Next.js.\n                      // For example edge-loader or app-loader.\n                      if (entryMod && entryMod.resource === '') {\n                        const moduleBuildInfo = getModuleBuildInfo(entryMod)\n                        // All loaders that are used to create entries have a `route` property on the buildInfo.\n                        if (moduleBuildInfo.route) {\n                          const absolutePath = getPageFilePath({\n                            absolutePagePath:\n                              moduleBuildInfo.route.absolutePagePath,\n                            rootDir: this.rootDir,\n                            appDir: this.appDir,\n                            pagesDir: this.pagesDir,\n                          })\n\n                          // Ensures we don't handle non-pages.\n                          if (\n                            (this.pagesDir &&\n                              absolutePath.startsWith(this.pagesDir)) ||\n                            (this.appDir &&\n                              absolutePath.startsWith(this.appDir))\n                          ) {\n                            entryModMap.set(absolutePath, entryMod)\n                            entryNameMap.set(absolutePath, name)\n                            absolutePathByEntryName.set(name, absolutePath)\n                          }\n                        }\n\n                        // If there was no `route` property, we can assume that it was something custom instead.\n                        // In order to trace these we add them to the additionalEntries map.\n                        if (entryMod.request) {\n                          let curMap = additionalEntries.get(name)\n\n                          if (!curMap) {\n                            curMap = new Map()\n                            additionalEntries.set(name, curMap)\n                          }\n                          depModMap.set(entryMod.request, entryMod)\n                          curMap.set(entryMod.resource, entryMod)\n                        }\n                      }\n\n                      if (entryMod && entryMod.resource) {\n                        entryNameMap.set(entryMod.resource, name)\n                        entryModMap.set(entryMod.resource, entryMod)\n\n                        let curMap = additionalEntries.get(name)\n\n                        if (!curMap) {\n                          curMap = new Map()\n                          additionalEntries.set(name, curMap)\n                        }\n                        depModMap.set(entryMod.resource, entryMod)\n                        curMap.set(entryMod.resource, entryMod)\n                      }\n                    }\n                  }\n                }\n              })\n\n            const readFile = async (\n              path: string\n            ): Promise<Buffer | string | null> => {\n              const mod = depModMap.get(path) || entryModMap.get(path)\n\n              // map the transpiled source when available to avoid\n              // parse errors in node-file-trace\n              let source: Buffer | string = mod?.originalSource?.()?.buffer()\n              return source || ''\n            }\n\n            const entryPaths = Array.from(entryModMap.keys())\n\n            const collectDependencies = async (mod: any, parent: string) => {\n              if (!mod || !mod.dependencies) return\n\n              for (const dep of mod.dependencies) {\n                const depMod = getModuleFromDependency(compilation, dep)\n\n                if (depMod?.resource && !depModMap.get(depMod.resource)) {\n                  depModMap.set(depMod.resource, depMod)\n                  await collectDependencies(depMod, parent)\n                }\n              }\n            }\n            const entriesToTrace = [...entryPaths]\n\n            for (const entry of entryPaths) {\n              await collectDependencies(entryModMap.get(entry), entry)\n              const entryName = entryNameMap.get(entry)!\n              const curExtraEntries = additionalEntries.get(entryName)\n\n              if (curExtraEntries) {\n                entriesToTrace.push(...curExtraEntries.keys())\n              }\n            }\n\n            const contextDirectory = this.tracingRoot\n            const chunks = [...entriesToTrace]\n\n            this.buildTraceContext.entriesTrace = {\n              action: {\n                action: 'print',\n                input: chunks,\n                contextDirectory,\n                processCwd: this.rootDir,\n              },\n              appDir: this.rootDir,\n              depModArray: Array.from(depModMap.keys()),\n              entryNameMap: Object.fromEntries(entryNameMap),\n              absolutePathByEntryName: Object.fromEntries(\n                absolutePathByEntryName\n              ),\n              outputPath: compilation.outputOptions.path!,\n            }\n\n            let fileList: Set<string>\n            let reasons: NodeFileTraceReasons\n            const ignores = [\n              ...TRACE_IGNORES,\n              ...this.traceIgnores,\n              '**/node_modules/**',\n            ]\n\n            // pre-compile the ignore matcher to avoid repeating on every ignoreFn call\n            const isIgnoreMatcher = picomatch(ignores, {\n              contains: true,\n              dot: true,\n            })\n            const ignoreFn = (path: string) => {\n              return isIgnoreMatcher(path)\n            }\n\n            await finishModulesSpan\n              .traceChild('node-file-trace-plugin', {\n                traceEntryCount: entriesToTrace.length + '',\n              })\n              .traceAsyncFn(async () => {\n                const result = await nodeFileTrace(entriesToTrace, {\n                  base: this.tracingRoot,\n                  processCwd: this.rootDir,\n                  readFile,\n                  readlink,\n                  stat,\n                  resolve: doResolve\n                    ? async (id, parent, job, isCjs) => {\n                        return doResolve(id, parent, job, !isCjs)\n                      }\n                    : undefined,\n                  ignore: ignoreFn,\n                  mixedModules: true,\n                })\n                // @ts-ignore\n                fileList = result.fileList\n                result.esmFileList.forEach((file) => fileList.add(file))\n                reasons = result.reasons\n              })\n\n            await finishModulesSpan\n              .traceChild('collect-traced-files')\n              .traceAsyncFn(() => {\n                const parentFilesMap = getFilesMapFromReasons(\n                  fileList,\n                  reasons,\n                  (file) => {\n                    // if a file was imported and a loader handled it\n                    // we don't include it in the trace e.g.\n                    // static image imports, CSS imports\n                    file = nodePath.join(this.tracingRoot, file)\n                    const depMod = depModMap.get(file)\n                    const isAsset = reasons\n                      .get(nodePath.relative(this.tracingRoot, file))\n                      ?.type.includes('asset')\n\n                    return (\n                      !isAsset &&\n                      Array.isArray(depMod?.loaders) &&\n                      depMod.loaders.length > 0\n                    )\n                  }\n                )\n\n                for (const entry of entryPaths) {\n                  const entryName = entryNameMap.get(entry)!\n                  const normalizedEntry = nodePath.relative(\n                    this.tracingRoot,\n                    entry\n                  )\n                  const curExtraEntries = additionalEntries.get(entryName)\n                  const finalDeps = new Map<string, { bundled: boolean }>()\n\n                  // ensure we include entry source file as well for\n                  // hash comparison\n                  finalDeps.set(entry, {\n                    bundled: true,\n                  })\n\n                  for (const [dep, info] of parentFilesMap\n                    .get(normalizedEntry)\n                    ?.entries() || []) {\n                    finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                      bundled: info.ignored,\n                    })\n                  }\n\n                  if (curExtraEntries) {\n                    for (const extraEntry of curExtraEntries.keys()) {\n                      const normalizedExtraEntry = nodePath.relative(\n                        this.tracingRoot,\n                        extraEntry\n                      )\n                      finalDeps.set(extraEntry, { bundled: false })\n\n                      for (const [dep, info] of parentFilesMap\n                        .get(normalizedExtraEntry)\n                        ?.entries() || []) {\n                        finalDeps.set(nodePath.join(this.tracingRoot, dep), {\n                          bundled: info.ignored,\n                        })\n                      }\n                    }\n                  }\n                  this.entryTraces.set(entryName, finalDeps)\n                }\n              })\n          })\n          .then(\n            () => callback(),\n            (err) => callback(err)\n          )\n      }\n    )\n  }\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      const compilationSpan =\n        getCompilationSpan(compilation) || getCompilationSpan(compiler)!\n      const traceEntrypointsPluginSpan = compilationSpan.traceChild(\n        'next-trace-entrypoint-plugin'\n      )\n\n      compilation.hooks.processAssets.tapAsync(\n        {\n          name: PLUGIN_NAME,\n          stage: webpack.Compilation.PROCESS_ASSETS_STAGE_SUMMARIZE,\n        },\n        (_assets: any, callback: any) => {\n          this.createTraceAssets(compilation, traceEntrypointsPluginSpan)\n            .then(() => callback())\n            .catch((err) => callback(err))\n        }\n      )\n\n      // rspack doesn't support all API below so only create trace assets\n      if (process.env.NEXT_RSPACK) {\n        return\n      }\n\n      const readlink = async (path: string): Promise<string | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(\n              compilation.inputFileSystem\n                .readlink as typeof import('fs').readlink\n            )(path, (err, link) => {\n              if (err) return reject(err)\n              resolve(link)\n            })\n          })\n        } catch (e) {\n          if (\n            isError(e) &&\n            (e.code === 'EINVAL' || e.code === 'ENOENT' || e.code === 'UNKNOWN')\n          ) {\n            return null\n          }\n          throw e\n        }\n      }\n      const stat = async (path: string): Promise<import('fs').Stats | null> => {\n        try {\n          return await new Promise((resolve, reject) => {\n            ;(compilation.inputFileSystem.stat as typeof import('fs').stat)(\n              path,\n              (err, stats) => {\n                if (err) return reject(err)\n                resolve(stats)\n              }\n            )\n          })\n        } catch (e) {\n          if (isError(e) && (e.code === 'ENOENT' || e.code === 'ENOTDIR')) {\n            return null\n          }\n          throw e\n        }\n      }\n\n      traceEntrypointsPluginSpan.traceFn(() => {\n        compilation.hooks.processAssets.tapAsync(\n          {\n            name: PLUGIN_NAME,\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_SUMMARIZE,\n          },\n          (_, callback: any) => {\n            this.createTraceAssets(compilation, traceEntrypointsPluginSpan)\n              .then(() => callback())\n              .catch((err) => callback(err))\n          }\n        )\n\n        let resolver = compilation.resolverFactory.get('normal')\n\n        function getPkgName(name: string) {\n          const segments = name.split('/')\n          if (name[0] === '@' && segments.length > 1)\n            return segments.length > 1 ? segments.slice(0, 2).join('/') : null\n          return segments.length ? segments[0] : null\n        }\n\n        const getResolve = (\n          options: Parameters<typeof resolver.withOptions>[0]\n        ) => {\n          const curResolver = resolver.withOptions(options)\n\n          return (\n            parent: string,\n            request: string,\n            job: import('@vercel/nft/out/node-file-trace').Job\n          ) =>\n            new Promise<[string, boolean]>((resolve, reject) => {\n              const context = nodePath.dirname(parent)\n\n              curResolver.resolve(\n                {},\n                context,\n                request,\n                {\n                  fileDependencies: compilation.fileDependencies,\n                  missingDependencies: compilation.missingDependencies,\n                  contextDependencies: compilation.contextDependencies,\n                },\n                async (err: any, result?, resContext?) => {\n                  if (err) return reject(err)\n\n                  if (!result) {\n                    return reject(new Error('module not found'))\n                  }\n\n                  // webpack resolver doesn't strip loader query info\n                  // from the result so use path instead\n                  if (result.includes('?') || result.includes('!')) {\n                    result = resContext?.path || result\n                  }\n\n                  try {\n                    // we need to collect all parent package.json's used\n                    // as webpack's resolve doesn't expose this and parent\n                    // package.json could be needed for resolving e.g. stylis\n                    // stylis/package.json -> stylis/dist/umd/package.json\n                    if (result.includes('node_modules')) {\n                      let requestPath = result\n                        .replace(/\\\\/g, '/')\n                        .replace(/\\0/g, '')\n\n                      if (\n                        !nodePath.isAbsolute(request) &&\n                        request.includes('/') &&\n                        resContext?.descriptionFileRoot\n                      ) {\n                        requestPath = (\n                          resContext.descriptionFileRoot +\n                          request.slice(getPkgName(request)?.length || 0) +\n                          nodePath.sep +\n                          'package.json'\n                        )\n                          .replace(/\\\\/g, '/')\n                          .replace(/\\0/g, '')\n                      }\n\n                      const rootSeparatorIndex = requestPath.indexOf('/')\n                      let separatorIndex: number\n                      while (\n                        (separatorIndex = requestPath.lastIndexOf('/')) >\n                        rootSeparatorIndex\n                      ) {\n                        requestPath = requestPath.slice(0, separatorIndex)\n                        const curPackageJsonPath = `${requestPath}/package.json`\n                        if (await job.isFile(curPackageJsonPath)) {\n                          await job.emitFile(\n                            await job.realpath(curPackageJsonPath),\n                            'resolve',\n                            parent\n                          )\n                        }\n                      }\n                    }\n                  } catch (_err) {\n                    // we failed to resolve the package.json boundary,\n                    // we don't block emitting the initial asset from this\n                  }\n                  resolve([result, options.dependencyType === 'esm'])\n                }\n              )\n            })\n        }\n\n        const CJS_RESOLVE_OPTIONS = {\n          ...NODE_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_CJS_RESOLVE_OPTIONS = {\n          ...CJS_RESOLVE_OPTIONS,\n          alias: false,\n        }\n        const ESM_RESOLVE_OPTIONS = {\n          ...NODE_ESM_RESOLVE_OPTIONS,\n          fullySpecified: undefined,\n          modules: undefined,\n          extensions: undefined,\n        }\n        const BASE_ESM_RESOLVE_OPTIONS = {\n          ...ESM_RESOLVE_OPTIONS,\n          alias: false,\n        }\n\n        const doResolve = async (\n          request: string,\n          parent: string,\n          job: import('@vercel/nft/out/node-file-trace').Job,\n          isEsmRequested: boolean\n        ): Promise<string> => {\n          const context = nodePath.dirname(parent)\n          // When in esm externals mode, and using import, we resolve with\n          // ESM resolving options.\n          const { res } = await resolveExternal(\n            this.rootDir,\n            this.esmExternals,\n            context,\n            request,\n            isEsmRequested,\n            (options) => (_: string, resRequest: string) => {\n              return getResolve(options)(parent, resRequest, job)\n            },\n            undefined,\n            undefined,\n            ESM_RESOLVE_OPTIONS,\n            CJS_RESOLVE_OPTIONS,\n            BASE_ESM_RESOLVE_OPTIONS,\n            BASE_CJS_RESOLVE_OPTIONS\n          )\n\n          if (!res) {\n            throw new Error(`failed to resolve ${request} from ${parent}`)\n          }\n          return res.replace(/\\0/g, '')\n        }\n\n        this.tapfinishModules(\n          compilation,\n          traceEntrypointsPluginSpan,\n          doResolve,\n          readlink,\n          stat\n        )\n      })\n    })\n  }\n}\n"], "names": ["TRACE_IGNORES", "TraceEntryPointsPlugin", "getFilesMapFromReasons", "PLUGIN_NAME", "NOT_TRACEABLE", "getModuleFromDependency", "compilation", "dep", "moduleGraph", "getModule", "fileList", "reasons", "ignoreFn", "parentFilesMap", "Map", "propagateToParents", "parents", "file", "seen", "Set", "parent", "has", "add", "parentFiles", "get", "set", "ignored", "Boolean", "parentReason", "reason", "isInitial", "type", "length", "includes", "size", "constructor", "rootDir", "appDir", "pagesDir", "compilerType", "appDirEnabled", "traceIgnores", "esmExternals", "outputFileTracingRoot", "buildTraceContext", "entryTraces", "tracingRoot", "createTraceAssets", "span", "outputPath", "outputOptions", "path", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "entryFilesMap", "chunksToTrace", "entryNameFilesMap", "isTraceable", "some", "suffix", "endsWith", "entrypoint", "entrypoints", "values", "entryFiles", "chunk", "process", "env", "NEXT_RSPACK", "chunks", "getEntrypointChunk", "getAllReferencedChunks", "files", "filePath", "nodePath", "join", "auxiliaryFiles", "name", "chunksTrace", "action", "input", "contextDirectory", "processCwd", "Object", "fromEntries", "outputPrefix", "traceOutputName", "traceOutputPath", "dirname", "delete", "startsWith", "appDirRelativeEntryPath", "entriesTrace", "absolutePathByEntryName", "replace", "entryIsStaticMetadataRoute", "isStaticMetadataRoute", "CLIENT_REFERENCE_MANIFEST", "finalFiles", "Promise", "all", "keys", "map", "fileInfo", "relativeFile", "relative", "bundled", "push", "emitAsset", "sources", "RawSource", "JSON", "stringify", "version", "TRACE_OUTPUT_VERSION", "tapfinishModules", "traceEntrypointsPluginSpan", "doResolve", "readlink", "stat", "hooks", "finishModules", "tapAsync", "_stats", "callback", "finishModulesSpan", "entryNameMap", "entryModMap", "additionalEntries", "depModMap", "entry", "entries", "normalizedName", "isPage", "isApp", "dependencies", "entryMod", "resource", "moduleBuildInfo", "getModuleBuildInfo", "route", "absolutePath", "getPageFilePath", "absolutePagePath", "request", "curMap", "readFile", "mod", "source", "originalSource", "buffer", "entryPaths", "Array", "from", "collectDependencies", "depMod", "entriesToTrace", "entryName", "curExtraEntries", "depModArray", "ignores", "isIgnoreMatcher", "picomatch", "contains", "dot", "traceEntryCount", "result", "nodeFileTrace", "base", "resolve", "id", "job", "isCjs", "undefined", "ignore", "mixedModules", "esmFileList", "for<PERSON>ach", "isAsset", "isArray", "loaders", "normalizedEntry", "finalDeps", "info", "extraEntry", "normalizedExtraEntry", "then", "err", "apply", "compiler", "tap", "compilationSpan", "getCompilationSpan", "processAssets", "stage", "webpack", "Compilation", "PROCESS_ASSETS_STAGE_SUMMARIZE", "_assets", "catch", "reject", "inputFileSystem", "link", "e", "isError", "code", "stats", "traceFn", "_", "resolver", "resolverFactory", "getPkgName", "segments", "split", "slice", "getResolve", "options", "curResolver", "withOptions", "context", "fileDependencies", "missingDependencies", "contextDependencies", "resContext", "Error", "requestPath", "isAbsolute", "descriptionFileRoot", "sep", "rootSeparatorIndex", "indexOf", "separatorIndex", "lastIndexOf", "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isFile", "emitFile", "realpath", "_err", "dependencyType", "CJS_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "fullySpecified", "modules", "extensions", "BASE_CJS_RESOLVE_OPTIONS", "alias", "ESM_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "BASE_ESM_RESOLVE_OPTIONS", "isEsmRequested", "res", "resolveExternal", "resRequest"], "mappings": ";;;;;;;;;;;;;;;;IAwBaA,aAAa;eAAbA;;IAuGAC,sBAAsB;eAAtBA;;IA9EGC,sBAAsB;eAAtBA;;;6DAjDK;gEAED;qBACU;2BAMvB;yBAC0B;+BAI1B;kEAEe;oCACa;yBACH;iCACA;iCACM;uBACH;;;;;;AAEnC,MAAMC,cAAc;AACb,MAAMH,gBAAgB;IAC3B;IACA;CACD;AAED,MAAMI,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,wBACPC,WAAgB,EAChBC,GAAQ;IAER,OAAOD,YAAYE,WAAW,CAACC,SAAS,CAACF;AAC3C;AAEO,SAASL,uBACdQ,QAAqB,EACrBC,OAA6B,EAC7BC,QAAqD;IAErD,4DAA4D;IAC5D,8DAA8D;IAC9D,aAAa;IACb,MAAMC,iBAAiB,IAAIC;IAE3B,SAASC,mBACPC,OAAoB,EACpBC,IAAY,EACZC,OAAO,IAAIC,KAAa;QAExB,KAAK,MAAMC,UAAUJ,WAAW,EAAE,CAAE;YAClC,IAAI,CAACE,KAAKG,GAAG,CAACD,SAAS;gBACrBF,KAAKI,GAAG,CAACF;gBACT,IAAIG,cAAcV,eAAeW,GAAG,CAACJ;gBAErC,IAAI,CAACG,aAAa;oBAChBA,cAAc,IAAIT;oBAClBD,eAAeY,GAAG,CAACL,QAAQG;gBAC7B;gBACA,MAAMG,UAAUC,QAAQf,4BAAAA,SAAWK,MAAMG;gBACzCG,YAAYE,GAAG,CAACR,MAAM;oBAAES;gBAAQ;gBAEhC,MAAME,eAAejB,QAAQa,GAAG,CAACJ;gBAEjC,IAAIQ,gCAAAA,aAAcZ,OAAO,EAAE;oBACzBD,mBAAmBa,aAAaZ,OAAO,EAAEC,MAAMC;gBACjD;YACF;QACF;IACF;IAEA,KAAK,MAAMD,QAAQP,SAAW;QAC5B,MAAMmB,SAASlB,QAASa,GAAG,CAACP;QAC5B,MAAMa,YACJD,CAAAA,0BAAAA,OAAQE,IAAI,CAACC,MAAM,MAAK,KAAKH,OAAOE,IAAI,CAACE,QAAQ,CAAC;QAEpD,IACE,CAACJ,UACD,CAACA,OAAOb,OAAO,IACdc,aAAaD,OAAOb,OAAO,CAACkB,IAAI,KAAK,GACtC;YACA;QACF;QACAnB,mBAAmBc,OAAOb,OAAO,EAAEC;IACrC;IACA,OAAOJ;AACT;AA2BO,MAAMZ;IAaXkC,YAAY,EACVC,OAAO,EACPC,MAAM,EACNC,QAAQ,EACRC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,YAAY,EACZC,qBAAqB,EAUtB,CAAE;aA9BIC,oBAAuC,CAAC;QA+B7C,IAAI,CAACR,OAAO,GAAGA;QACf,IAAI,CAACC,MAAM,GAAGA;QACd,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACO,WAAW,GAAG,IAAI/B;QACvB,IAAI,CAAC4B,YAAY,GAAGA;QACpB,IAAI,CAACF,aAAa,GAAGA;QACrB,IAAI,CAACC,YAAY,GAAGA,gBAAgB,EAAE;QACtC,IAAI,CAACK,WAAW,GAAGH,yBAAyBP;QAC5C,IAAI,CAACG,YAAY,GAAGA;IACtB;IAEA,2DAA2D;IAC3D,2BAA2B;IAC3B,MAAMQ,kBAAkBzC,WAAgC,EAAE0C,IAAU,EAAE;QACpE,MAAMC,aAAa3C,YAAY4C,aAAa,CAACC,IAAI,IAAI;QAErD,MAAMH,KAAKI,UAAU,CAAC,uBAAuBC,YAAY,CAAC;YACxD,MAAMC,gBAAgB,IAAIxC;YAC1B,MAAMyC,gBAAgB,IAAIpC;YAC1B,MAAMqC,oBAAoB,IAAI1C;YAE9B,MAAM2C,cAAc,CAACxC,OACnB,CAACb,cAAcsD,IAAI,CAAC,CAACC;oBACnB,OAAO1C,KAAK2C,QAAQ,CAACD;gBACvB;YAEF,KAAK,MAAME,cAAcvD,YAAYwD,WAAW,CAACC,MAAM,GAAI;gBACzD,MAAMC,aAAa,IAAI7C;gBAEvB,KAAK,MAAM8C,SAASC,QAAQC,GAAG,CAACC,WAAW,GACvCP,WAAWQ,MAAM,GACjBR,WAAWS,kBAAkB,GAAGC,sBAAsB,GAAI;oBAC5D,KAAK,MAAMtD,QAAQgD,MAAMO,KAAK,CAAE;wBAC9B,IAAIf,YAAYxC,OAAO;4BACrB,MAAMwD,WAAWC,aAAQ,CAACC,IAAI,CAAC1B,YAAYhC;4BAC3CsC,cAAcjC,GAAG,CAACmD;4BAClBT,WAAW1C,GAAG,CAACmD;wBACjB;oBACF;oBACA,KAAK,MAAMxD,QAAQgD,MAAMW,cAAc,CAAE;wBACvC,IAAInB,YAAYxC,OAAO;4BACrB,MAAMwD,WAAWC,aAAQ,CAACC,IAAI,CAAC1B,YAAYhC;4BAC3CsC,cAAcjC,GAAG,CAACmD;4BAClBT,WAAW1C,GAAG,CAACmD;wBACjB;oBACF;gBACF;gBACAnB,cAAc7B,GAAG,CAACoC,YAAYG;gBAC9BR,kBAAkB/B,GAAG,CAACoC,WAAWgB,IAAI,IAAI,IAAI;uBAAIb;iBAAW;YAC9D;YAEA,kCAAkC;YAClC,IAAI,CAACpB,iBAAiB,CAACkC,WAAW,GAAG;gBACnCC,QAAQ;oBACNA,QAAQ;oBACRC,OAAO;2BAAIzB;qBAAc;oBACzB0B,kBAAkB,IAAI,CAACnC,WAAW;oBAClCoC,YAAY,IAAI,CAAC9C,OAAO;gBAC1B;gBACAa;gBACAO,mBAAmB2B,OAAOC,WAAW,CAAC5B;YACxC;YAEA,+DAA+D;YAC/D,sDAAsD;YACtD,MAAM6B,eAAe,IAAI,CAAC9C,YAAY,KAAK,WAAW,QAAQ;YAE9D,KAAK,MAAM,CAACsB,YAAYG,WAAW,IAAIV,cAAe;oBA2C1C;gBA1CV,MAAMgC,kBAAkB,GAAGD,eAAexB,WAAWgB,IAAI,CAAC,YAAY,CAAC;gBACvE,MAAMU,kBAAkBb,aAAQ,CAACc,OAAO,CACtCd,aAAQ,CAACC,IAAI,CAAC1B,YAAYqC;gBAG5B,8CAA8C;gBAC9CtB,WAAWyB,MAAM,CACff,aAAQ,CAACC,IAAI,CAAC1B,YAAY,GAAGoC,eAAexB,WAAWgB,IAAI,CAAC,GAAG,CAAC;gBAGlE,IAAIhB,WAAWgB,IAAI,CAACa,UAAU,CAAC,WAAW,IAAI,CAACrD,MAAM,EAAE;wBAEnD,8EAAA;oBADF,MAAMsD,2BACJ,uCAAA,IAAI,CAAC/C,iBAAiB,CAACgD,YAAY,sBAAnC,+EAAA,qCAAqCC,uBAAuB,CAC1DhC,WAAWgB,IAAI,CAChB,qBAFD,6EAEGiB,OAAO,CAAC,IAAI,CAACzD,MAAM,EAAE;oBAE1B,MAAM0D,6BACJJ,2BACAK,IAAAA,sCAAqB,EAACL;oBAExB,kEAAkE;oBAClE,6DAA6D;oBAC7D,IAAI,CAACI,4BAA4B;wBAC/B/B,WAAW1C,GAAG,CACZoD,aAAQ,CAACC,IAAI,CACX1B,YACAoC,cACAxB,WAAWgB,IAAI,CAACiB,OAAO,CAAC,QAAQ,OAC9B,MACAG,oCAAyB,GACzB;oBAGR;gBACF;gBAEA,MAAMC,aAAuB,EAAE;gBAE/B,MAAMC,QAAQC,GAAG,CACf;uBACK,IAAIjF,IAAI;2BACN6C;2BACC,EAAA,wBAAA,IAAI,CAACnB,WAAW,CAACrB,GAAG,CAACqC,WAAWgB,IAAI,sBAApC,sBAAuCwB,IAAI,OAAM,EAAE;qBACxD;iBACF,CAACC,GAAG,CAAC,OAAOrF;wBACM;oBAAjB,MAAMsF,YAAW,wBAAA,IAAI,CAAC1D,WAAW,CAACrB,GAAG,CAACqC,WAAWgB,IAAI,sBAApC,sBAAuCrD,GAAG,CAACP;oBAE5D,MAAMuF,eAAe9B,aAAQ,CAC1B+B,QAAQ,CAAClB,iBAAiBtE,MAC1B6E,OAAO,CAAC,OAAO;oBAElB,IAAI7E,MAAM;wBACR,IAAI,EAACsF,4BAAAA,SAAUG,OAAO,GAAE;4BACtBR,WAAWS,IAAI,CAACH;wBAClB;oBACF;gBACF;gBAGFlG,YAAYsG,SAAS,CACnBtB,iBACA,IAAIuB,gBAAO,CAACC,SAAS,CACnBC,KAAKC,SAAS,CAAC;oBACbC,SAASC,+BAAoB;oBAC7B1C,OAAO0B;gBACT;YAGN;QACF;IACF;IAEAiB,iBACE7G,WAAgC,EAChC8G,0BAAgC,EAChCC,SAKoB,EACpBC,QAAa,EACbC,IAAS,EACT;QACAjH,YAAYkH,KAAK,CAACC,aAAa,CAACC,QAAQ,CACtCvH,aACA,OAAOwH,QAAaC;YAClB,MAAMC,oBACJT,2BAA2BhE,UAAU,CAAC;YACxC,MAAMyE,kBACHxE,YAAY,CAAC;gBACZ,gDAAgD;gBAChD,mDAAmD;gBACnD,oCAAoC;gBACpC,MAAMyE,eAAe,IAAIhH;gBACzB,MAAMiH,cAAc,IAAIjH;gBACxB,MAAMkH,oBAAoB,IAAIlH;gBAC9B,MAAM+E,0BAA0B,IAAI/E;gBAEpC,MAAMmH,YAAY,IAAInH;gBAEtB,MAAM+G,kBACHzE,UAAU,CAAC,eACXC,YAAY,CAAC;oBACZ,KAAK,MAAM,CAACwB,MAAMqD,MAAM,IAAI5H,YAAY6H,OAAO,CAACA,OAAO,GAAI;wBACzD,MAAMC,iBAAiBvD,wBAAAA,KAAMiB,OAAO,CAAC,OAAO;wBAE5C,MAAMuC,SAASD,eAAe1C,UAAU,CAAC;wBACzC,MAAM4C,QACJ,IAAI,CAAC9F,aAAa,IAAI4F,eAAe1C,UAAU,CAAC;wBAElD,IAAI4C,SAASD,QAAQ;4BACnB,KAAK,MAAM9H,OAAO2H,MAAMK,YAAY,CAAE;gCACpC,IAAI,CAAChI,KAAK;gCACV,MAAMiI,WAAWnI,wBAAwBC,aAAaC;gCAEtD,2DAA2D;gCAC3D,yCAAyC;gCACzC,IAAIiI,YAAYA,SAASC,QAAQ,KAAK,IAAI;oCACxC,MAAMC,kBAAkBC,IAAAA,sCAAkB,EAACH;oCAC3C,wFAAwF;oCACxF,IAAIE,gBAAgBE,KAAK,EAAE;wCACzB,MAAMC,eAAeC,IAAAA,wBAAe,EAAC;4CACnCC,kBACEL,gBAAgBE,KAAK,CAACG,gBAAgB;4CACxC3G,SAAS,IAAI,CAACA,OAAO;4CACrBC,QAAQ,IAAI,CAACA,MAAM;4CACnBC,UAAU,IAAI,CAACA,QAAQ;wCACzB;wCAEA,qCAAqC;wCACrC,IACE,AAAC,IAAI,CAACA,QAAQ,IACZuG,aAAanD,UAAU,CAAC,IAAI,CAACpD,QAAQ,KACtC,IAAI,CAACD,MAAM,IACVwG,aAAanD,UAAU,CAAC,IAAI,CAACrD,MAAM,GACrC;4CACA0F,YAAYtG,GAAG,CAACoH,cAAcL;4CAC9BV,aAAarG,GAAG,CAACoH,cAAchE;4CAC/BgB,wBAAwBpE,GAAG,CAACoD,MAAMgE;wCACpC;oCACF;oCAEA,wFAAwF;oCACxF,oEAAoE;oCACpE,IAAIL,SAASQ,OAAO,EAAE;wCACpB,IAAIC,SAASjB,kBAAkBxG,GAAG,CAACqD;wCAEnC,IAAI,CAACoE,QAAQ;4CACXA,SAAS,IAAInI;4CACbkH,kBAAkBvG,GAAG,CAACoD,MAAMoE;wCAC9B;wCACAhB,UAAUxG,GAAG,CAAC+G,SAASQ,OAAO,EAAER;wCAChCS,OAAOxH,GAAG,CAAC+G,SAASC,QAAQ,EAAED;oCAChC;gCACF;gCAEA,IAAIA,YAAYA,SAASC,QAAQ,EAAE;oCACjCX,aAAarG,GAAG,CAAC+G,SAASC,QAAQ,EAAE5D;oCACpCkD,YAAYtG,GAAG,CAAC+G,SAASC,QAAQ,EAAED;oCAEnC,IAAIS,SAASjB,kBAAkBxG,GAAG,CAACqD;oCAEnC,IAAI,CAACoE,QAAQ;wCACXA,SAAS,IAAInI;wCACbkH,kBAAkBvG,GAAG,CAACoD,MAAMoE;oCAC9B;oCACAhB,UAAUxG,GAAG,CAAC+G,SAASC,QAAQ,EAAED;oCACjCS,OAAOxH,GAAG,CAAC+G,SAASC,QAAQ,EAAED;gCAChC;4BACF;wBACF;oBACF;gBACF;gBAEF,MAAMU,WAAW,OACf/F;wBAM8BgG,qBAAAA;oBAJ9B,MAAMA,MAAMlB,UAAUzG,GAAG,CAAC2B,SAAS4E,YAAYvG,GAAG,CAAC2B;oBAEnD,oDAAoD;oBACpD,kCAAkC;oBAClC,IAAIiG,SAA0BD,wBAAAA,uBAAAA,IAAKE,cAAc,sBAAnBF,sBAAAA,0BAAAA,yBAAAA,oBAAyBG,MAAM;oBAC7D,OAAOF,UAAU;gBACnB;gBAEA,MAAMG,aAAaC,MAAMC,IAAI,CAAC1B,YAAY1B,IAAI;gBAE9C,MAAMqD,sBAAsB,OAAOP,KAAU/H;oBAC3C,IAAI,CAAC+H,OAAO,CAACA,IAAIZ,YAAY,EAAE;oBAE/B,KAAK,MAAMhI,OAAO4I,IAAIZ,YAAY,CAAE;wBAClC,MAAMoB,SAAStJ,wBAAwBC,aAAaC;wBAEpD,IAAIoJ,CAAAA,0BAAAA,OAAQlB,QAAQ,KAAI,CAACR,UAAUzG,GAAG,CAACmI,OAAOlB,QAAQ,GAAG;4BACvDR,UAAUxG,GAAG,CAACkI,OAAOlB,QAAQ,EAAEkB;4BAC/B,MAAMD,oBAAoBC,QAAQvI;wBACpC;oBACF;gBACF;gBACA,MAAMwI,iBAAiB;uBAAIL;iBAAW;gBAEtC,KAAK,MAAMrB,SAASqB,WAAY;oBAC9B,MAAMG,oBAAoB3B,YAAYvG,GAAG,CAAC0G,QAAQA;oBAClD,MAAM2B,YAAY/B,aAAatG,GAAG,CAAC0G;oBACnC,MAAM4B,kBAAkB9B,kBAAkBxG,GAAG,CAACqI;oBAE9C,IAAIC,iBAAiB;wBACnBF,eAAejD,IAAI,IAAImD,gBAAgBzD,IAAI;oBAC7C;gBACF;gBAEA,MAAMpB,mBAAmB,IAAI,CAACnC,WAAW;gBACzC,MAAMuB,SAAS;uBAAIuF;iBAAe;gBAElC,IAAI,CAAChH,iBAAiB,CAACgD,YAAY,GAAG;oBACpCb,QAAQ;wBACNA,QAAQ;wBACRC,OAAOX;wBACPY;wBACAC,YAAY,IAAI,CAAC9C,OAAO;oBAC1B;oBACAC,QAAQ,IAAI,CAACD,OAAO;oBACpB2H,aAAaP,MAAMC,IAAI,CAACxB,UAAU5B,IAAI;oBACtCyB,cAAc3C,OAAOC,WAAW,CAAC0C;oBACjCjC,yBAAyBV,OAAOC,WAAW,CACzCS;oBAEF5C,YAAY3C,YAAY4C,aAAa,CAACC,IAAI;gBAC5C;gBAEA,IAAIzC;gBACJ,IAAIC;gBACJ,MAAMqJ,UAAU;uBACXhK;uBACA,IAAI,CAACyC,YAAY;oBACpB;iBACD;gBAED,2EAA2E;gBAC3E,MAAMwH,kBAAkBC,IAAAA,kBAAS,EAACF,SAAS;oBACzCG,UAAU;oBACVC,KAAK;gBACP;gBACA,MAAMxJ,WAAW,CAACuC;oBAChB,OAAO8G,gBAAgB9G;gBACzB;gBAEA,MAAM0E,kBACHzE,UAAU,CAAC,0BAA0B;oBACpCiH,iBAAiBT,eAAe5H,MAAM,GAAG;gBAC3C,GACCqB,YAAY,CAAC;oBACZ,MAAMiH,SAAS,MAAMC,IAAAA,kBAAa,EAACX,gBAAgB;wBACjDY,MAAM,IAAI,CAAC1H,WAAW;wBACtBoC,YAAY,IAAI,CAAC9C,OAAO;wBACxB8G;wBACA5B;wBACAC;wBACAkD,SAASpD,YACL,OAAOqD,IAAItJ,QAAQuJ,KAAKC;4BACtB,OAAOvD,UAAUqD,IAAItJ,QAAQuJ,KAAK,CAACC;wBACrC,IACAC;wBACJC,QAAQlK;wBACRmK,cAAc;oBAChB;oBACA,aAAa;oBACbrK,WAAW4J,OAAO5J,QAAQ;oBAC1B4J,OAAOU,WAAW,CAACC,OAAO,CAAC,CAAChK,OAASP,SAASY,GAAG,CAACL;oBAClDN,UAAU2J,OAAO3J,OAAO;gBAC1B;gBAEF,MAAMkH,kBACHzE,UAAU,CAAC,wBACXC,YAAY,CAAC;oBACZ,MAAMxC,iBAAiBX,uBACrBQ,UACAC,SACA,CAACM;4BAMiBN;wBALhB,iDAAiD;wBACjD,wCAAwC;wBACxC,oCAAoC;wBACpCM,OAAOyD,aAAQ,CAACC,IAAI,CAAC,IAAI,CAAC7B,WAAW,EAAE7B;wBACvC,MAAM0I,SAAS1B,UAAUzG,GAAG,CAACP;wBAC7B,MAAMiK,WAAUvK,eAAAA,QACba,GAAG,CAACkD,aAAQ,CAAC+B,QAAQ,CAAC,IAAI,CAAC3D,WAAW,EAAE7B,2BAD3BN,aAEZoB,IAAI,CAACE,QAAQ,CAAC;wBAElB,OACE,CAACiJ,WACD1B,MAAM2B,OAAO,CAACxB,0BAAAA,OAAQyB,OAAO,KAC7BzB,OAAOyB,OAAO,CAACpJ,MAAM,GAAG;oBAE5B;oBAGF,KAAK,MAAMkG,SAASqB,WAAY;4BAeJ1I;wBAd1B,MAAMgJ,YAAY/B,aAAatG,GAAG,CAAC0G;wBACnC,MAAMmD,kBAAkB3G,aAAQ,CAAC+B,QAAQ,CACvC,IAAI,CAAC3D,WAAW,EAChBoF;wBAEF,MAAM4B,kBAAkB9B,kBAAkBxG,GAAG,CAACqI;wBAC9C,MAAMyB,YAAY,IAAIxK;wBAEtB,kDAAkD;wBAClD,kBAAkB;wBAClBwK,UAAU7J,GAAG,CAACyG,OAAO;4BACnBxB,SAAS;wBACX;wBAEA,KAAK,MAAM,CAACnG,KAAKgL,KAAK,IAAI1K,EAAAA,sBAAAA,eACvBW,GAAG,CAAC6J,qCADmBxK,oBAEtBsH,OAAO,OAAM,EAAE,CAAE;4BACnBmD,UAAU7J,GAAG,CAACiD,aAAQ,CAACC,IAAI,CAAC,IAAI,CAAC7B,WAAW,EAAEvC,MAAM;gCAClDmG,SAAS6E,KAAK7J,OAAO;4BACvB;wBACF;wBAEA,IAAIoI,iBAAiB;4BACnB,KAAK,MAAM0B,cAAc1B,gBAAgBzD,IAAI,GAAI;oCAOrBxF;gCAN1B,MAAM4K,uBAAuB/G,aAAQ,CAAC+B,QAAQ,CAC5C,IAAI,CAAC3D,WAAW,EAChB0I;gCAEFF,UAAU7J,GAAG,CAAC+J,YAAY;oCAAE9E,SAAS;gCAAM;gCAE3C,KAAK,MAAM,CAACnG,KAAKgL,KAAK,IAAI1K,EAAAA,uBAAAA,eACvBW,GAAG,CAACiK,0CADmB5K,qBAEtBsH,OAAO,OAAM,EAAE,CAAE;oCACnBmD,UAAU7J,GAAG,CAACiD,aAAQ,CAACC,IAAI,CAAC,IAAI,CAAC7B,WAAW,EAAEvC,MAAM;wCAClDmG,SAAS6E,KAAK7J,OAAO;oCACvB;gCACF;4BACF;wBACF;wBACA,IAAI,CAACmB,WAAW,CAACpB,GAAG,CAACoI,WAAWyB;oBAClC;gBACF;YACJ,GACCI,IAAI,CACH,IAAM9D,YACN,CAAC+D,MAAQ/D,SAAS+D;QAExB;IAEJ;IAEAC,MAAMC,QAA0B,EAAE;QAChCA,SAASrE,KAAK,CAAClH,WAAW,CAACwL,GAAG,CAAC3L,aAAa,CAACG;YAC3C,MAAMyL,kBACJC,IAAAA,yBAAkB,EAAC1L,gBAAgB0L,IAAAA,yBAAkB,EAACH;YACxD,MAAMzE,6BAA6B2E,gBAAgB3I,UAAU,CAC3D;YAGF9C,YAAYkH,KAAK,CAACyE,aAAa,CAACvE,QAAQ,CACtC;gBACE7C,MAAM1E;gBACN+L,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACC,SAAc1E;gBACb,IAAI,CAAC7E,iBAAiB,CAACzC,aAAa8G,4BACjCsE,IAAI,CAAC,IAAM9D,YACX2E,KAAK,CAAC,CAACZ,MAAQ/D,SAAS+D;YAC7B;YAGF,mEAAmE;YACnE,IAAIzH,QAAQC,GAAG,CAACC,WAAW,EAAE;gBAC3B;YACF;YAEA,MAAMkD,WAAW,OAAOnE;gBACtB,IAAI;oBACF,OAAO,MAAM,IAAIgD,QAAQ,CAACsE,SAAS+B;;wBAE/BlM,YAAYmM,eAAe,CACxBnF,QAAQ,CACXnE,MAAM,CAACwI,KAAKe;4BACZ,IAAIf,KAAK,OAAOa,OAAOb;4BACvBlB,QAAQiC;wBACV;oBACF;gBACF,EAAE,OAAOC,GAAG;oBACV,IACEC,IAAAA,gBAAO,EAACD,MACPA,CAAAA,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,SAAQ,GAClE;wBACA,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YACA,MAAMpF,OAAO,OAAOpE;gBAClB,IAAI;oBACF,OAAO,MAAM,IAAIgD,QAAQ,CAACsE,SAAS+B;;wBAC/BlM,YAAYmM,eAAe,CAAClF,IAAI,CAChCpE,MACA,CAACwI,KAAKmB;4BACJ,IAAInB,KAAK,OAAOa,OAAOb;4BACvBlB,QAAQqC;wBACV;oBAEJ;gBACF,EAAE,OAAOH,GAAG;oBACV,IAAIC,IAAAA,gBAAO,EAACD,MAAOA,CAAAA,EAAEE,IAAI,KAAK,YAAYF,EAAEE,IAAI,KAAK,SAAQ,GAAI;wBAC/D,OAAO;oBACT;oBACA,MAAMF;gBACR;YACF;YAEAvF,2BAA2B2F,OAAO,CAAC;gBACjCzM,YAAYkH,KAAK,CAACyE,aAAa,CAACvE,QAAQ,CACtC;oBACE7C,MAAM1E;oBACN+L,OAAOC,gBAAO,CAACC,WAAW,CAACC,8BAA8B;gBAC3D,GACA,CAACW,GAAGpF;oBACF,IAAI,CAAC7E,iBAAiB,CAACzC,aAAa8G,4BACjCsE,IAAI,CAAC,IAAM9D,YACX2E,KAAK,CAAC,CAACZ,MAAQ/D,SAAS+D;gBAC7B;gBAGF,IAAIsB,WAAW3M,YAAY4M,eAAe,CAAC1L,GAAG,CAAC;gBAE/C,SAAS2L,WAAWtI,IAAY;oBAC9B,MAAMuI,WAAWvI,KAAKwI,KAAK,CAAC;oBAC5B,IAAIxI,IAAI,CAAC,EAAE,KAAK,OAAOuI,SAASpL,MAAM,GAAG,GACvC,OAAOoL,SAASpL,MAAM,GAAG,IAAIoL,SAASE,KAAK,CAAC,GAAG,GAAG3I,IAAI,CAAC,OAAO;oBAChE,OAAOyI,SAASpL,MAAM,GAAGoL,QAAQ,CAAC,EAAE,GAAG;gBACzC;gBAEA,MAAMG,aAAa,CACjBC;oBAEA,MAAMC,cAAcR,SAASS,WAAW,CAACF;oBAEzC,OAAO,CACLpM,QACA4H,SACA2B,MAEA,IAAIxE,QAA2B,CAACsE,SAAS+B;4BACvC,MAAMmB,UAAUjJ,aAAQ,CAACc,OAAO,CAACpE;4BAEjCqM,YAAYhD,OAAO,CACjB,CAAC,GACDkD,SACA3E,SACA;gCACE4E,kBAAkBtN,YAAYsN,gBAAgB;gCAC9CC,qBAAqBvN,YAAYuN,mBAAmB;gCACpDC,qBAAqBxN,YAAYwN,mBAAmB;4BACtD,GACA,OAAOnC,KAAUrB,QAASyD;gCACxB,IAAIpC,KAAK,OAAOa,OAAOb;gCAEvB,IAAI,CAACrB,QAAQ;oCACX,OAAOkC,OAAO,qBAA6B,CAA7B,IAAIwB,MAAM,qBAAV,qBAAA;+CAAA;oDAAA;sDAAA;oCAA4B;gCAC5C;gCAEA,mDAAmD;gCACnD,sCAAsC;gCACtC,IAAI1D,OAAOrI,QAAQ,CAAC,QAAQqI,OAAOrI,QAAQ,CAAC,MAAM;oCAChDqI,SAASyD,CAAAA,8BAAAA,WAAY5K,IAAI,KAAImH;gCAC/B;gCAEA,IAAI;oCACF,oDAAoD;oCACpD,sDAAsD;oCACtD,yDAAyD;oCACzD,sDAAsD;oCACtD,IAAIA,OAAOrI,QAAQ,CAAC,iBAAiB;wCACnC,IAAIgM,cAAc3D,OACfxE,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCAElB,IACE,CAACpB,aAAQ,CAACwJ,UAAU,CAAClF,YACrBA,QAAQ/G,QAAQ,CAAC,SACjB8L,8BAAAA,WAAYI,mBAAmB,GAC/B;gDAGgBhB;4CAFhBc,cAAc,AACZF,CAAAA,WAAWI,mBAAmB,GAC9BnF,QAAQsE,KAAK,CAACH,EAAAA,cAAAA,WAAWnE,6BAAXmE,YAAqBnL,MAAM,KAAI,KAC7C0C,aAAQ,CAAC0J,GAAG,GACZ,cAAa,EAEZtI,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,OAAO;wCACpB;wCAEA,MAAMuI,qBAAqBJ,YAAYK,OAAO,CAAC;wCAC/C,IAAIC;wCACJ,MACE,AAACA,CAAAA,iBAAiBN,YAAYO,WAAW,CAAC,IAAG,IAC7CH,mBACA;4CACAJ,cAAcA,YAAYX,KAAK,CAAC,GAAGiB;4CACnC,MAAME,qBAAqB,GAAGR,YAAY,aAAa,CAAC;4CACxD,IAAI,MAAMtD,IAAI+D,MAAM,CAACD,qBAAqB;gDACxC,MAAM9D,IAAIgE,QAAQ,CAChB,MAAMhE,IAAIiE,QAAQ,CAACH,qBACnB,WACArN;4CAEJ;wCACF;oCACF;gCACF,EAAE,OAAOyN,MAAM;gCACb,kDAAkD;gCAClD,sDAAsD;gCACxD;gCACApE,QAAQ;oCAACH;oCAAQkD,QAAQsB,cAAc,KAAK;iCAAM;4BACpD;wBAEJ;gBACJ;gBAEA,MAAMC,sBAAsB;oBAC1B,GAAGC,mCAAoB;oBACvBC,gBAAgBpE;oBAChBqE,SAASrE;oBACTsE,YAAYtE;gBACd;gBACA,MAAMuE,2BAA2B;oBAC/B,GAAGL,mBAAmB;oBACtBM,OAAO;gBACT;gBACA,MAAMC,sBAAsB;oBAC1B,GAAGC,uCAAwB;oBAC3BN,gBAAgBpE;oBAChBqE,SAASrE;oBACTsE,YAAYtE;gBACd;gBACA,MAAM2E,2BAA2B;oBAC/B,GAAGF,mBAAmB;oBACtBD,OAAO;gBACT;gBAEA,MAAMhI,YAAY,OAChB2B,SACA5H,QACAuJ,KACA8E;oBAEA,MAAM9B,UAAUjJ,aAAQ,CAACc,OAAO,CAACpE;oBACjC,gEAAgE;oBAChE,yBAAyB;oBACzB,MAAM,EAAEsO,GAAG,EAAE,GAAG,MAAMC,IAAAA,gCAAe,EACnC,IAAI,CAACvN,OAAO,EACZ,IAAI,CAACM,YAAY,EACjBiL,SACA3E,SACAyG,gBACA,CAACjC,UAAY,CAACR,GAAW4C;4BACvB,OAAOrC,WAAWC,SAASpM,QAAQwO,YAAYjF;wBACjD,GACAE,WACAA,WACAyE,qBACAP,qBACAS,0BACAJ;oBAGF,IAAI,CAACM,KAAK;wBACR,MAAM,qBAAwD,CAAxD,IAAI1B,MAAM,CAAC,kBAAkB,EAAEhF,QAAQ,MAAM,EAAE5H,QAAQ,GAAvD,qBAAA;mCAAA;wCAAA;0CAAA;wBAAuD;oBAC/D;oBACA,OAAOsO,IAAI5J,OAAO,CAAC,OAAO;gBAC5B;gBAEA,IAAI,CAACqB,gBAAgB,CACnB7G,aACA8G,4BACAC,WACAC,UACAC;YAEJ;QACF;IACF;AACF"}