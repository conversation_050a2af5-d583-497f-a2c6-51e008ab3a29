{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-image-loader/index.ts"], "sourcesContent": ["import type { CompilerNameValues } from '../../../../shared/lib/constants'\n\nimport path from 'path'\nimport loaderUtils from 'next/dist/compiled/loader-utils3'\nimport { getImageSize } from '../../../../server/image-optimizer'\nimport { getBlurImage } from './blur'\n\ninterface Options {\n  compilerType: CompilerNameValues\n  isDev: boolean\n  assetPrefix: string\n  basePath: string\n}\n\nfunction nextImageLoader(this: any, content: Buffer) {\n  const imageLoaderSpan = this.currentTraceSpan.traceChild('next-image-loader')\n  return imageLoaderSpan.traceAsyncFn(async () => {\n    const options: Options = this.getOptions()\n    const { compilerType, isDev, assetPrefix, basePath } = options\n    const context = this.rootContext\n\n    const opts = { context, content }\n    const interpolatedName = loaderUtils.interpolateName(\n      this,\n      '/static/media/[name].[hash:8].[ext]',\n      opts\n    )\n    const outputPath = assetPrefix + '/_next' + interpolatedName\n    let extension = loaderUtils.interpolateName(this, '[ext]', opts)\n    if (extension === 'jpg') {\n      extension = 'jpeg'\n    }\n\n    const imageSizeSpan = imageLoaderSpan.traceChild('image-size-calculation')\n    const imageSize = await imageSizeSpan.traceAsyncFn(() =>\n      getImageSize(content).catch((err) => err)\n    )\n\n    if (imageSize instanceof Error) {\n      const err = imageSize\n      err.name = 'InvalidImageFormatError'\n      throw err\n    }\n\n    const {\n      dataURL: blurDataURL,\n      width: blurWidth,\n      height: blurHeight,\n    } = await getBlurImage(content, extension, imageSize, {\n      basePath,\n      outputPath,\n      isDev,\n      tracing: imageLoaderSpan.traceChild.bind(imageLoaderSpan),\n    })\n\n    const stringifiedData = imageLoaderSpan\n      .traceChild('image-data-stringify')\n      .traceFn(() =>\n        JSON.stringify({\n          src: outputPath,\n          height: imageSize.height,\n          width: imageSize.width,\n          blurDataURL,\n          blurWidth,\n          blurHeight,\n        })\n      )\n\n    if (compilerType === 'client') {\n      this.emitFile(interpolatedName, content, null)\n    } else {\n      this.emitFile(\n        path.join(\n          '..',\n          isDev || compilerType === 'edge-server' ? '' : '..',\n          interpolatedName\n        ),\n        content,\n        null\n      )\n    }\n\n    return `export default ${stringifiedData};`\n  })\n}\nexport const raw = true\nexport default nextImageLoader\n"], "names": ["raw", "nextImage<PERSON><PERSON><PERSON>", "content", "imageLoaderSpan", "currentTraceSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "options", "getOptions", "compilerType", "isDev", "assetPrefix", "basePath", "context", "rootContext", "opts", "interpolatedName", "loaderUtils", "interpolateName", "outputPath", "extension", "imageSizeSpan", "imageSize", "getImageSize", "catch", "err", "Error", "name", "dataURL", "blurDataURL", "width", "blur<PERSON>idth", "height", "blurHeight", "getBlurImage", "tracing", "bind", "stringifiedData", "traceFn", "JSON", "stringify", "src", "emitFile", "path", "join"], "mappings": ";;;;;;;;;;;;;;;IAsFA,OAA8B;eAA9B;;IADaA,GAAG;eAAHA;;;6DAnFI;qEACO;gCACK;sBACA;;;;;;AAS7B,SAASC,gBAA2BC,OAAe;IACjD,MAAMC,kBAAkB,IAAI,CAACC,gBAAgB,CAACC,UAAU,CAAC;IACzD,OAAOF,gBAAgBG,YAAY,CAAC;QAClC,MAAMC,UAAmB,IAAI,CAACC,UAAU;QACxC,MAAM,EAAEC,YAAY,EAAEC,KAAK,EAAEC,WAAW,EAAEC,QAAQ,EAAE,GAAGL;QACvD,MAAMM,UAAU,IAAI,CAACC,WAAW;QAEhC,MAAMC,OAAO;YAAEF;YAASX;QAAQ;QAChC,MAAMc,mBAAmBC,qBAAW,CAACC,eAAe,CAClD,IAAI,EACJ,uCACAH;QAEF,MAAMI,aAAaR,cAAc,WAAWK;QAC5C,IAAII,YAAYH,qBAAW,CAACC,eAAe,CAAC,IAAI,EAAE,SAASH;QAC3D,IAAIK,cAAc,OAAO;YACvBA,YAAY;QACd;QAEA,MAAMC,gBAAgBlB,gBAAgBE,UAAU,CAAC;QACjD,MAAMiB,YAAY,MAAMD,cAAcf,YAAY,CAAC,IACjDiB,IAAAA,4BAAY,EAACrB,SAASsB,KAAK,CAAC,CAACC,MAAQA;QAGvC,IAAIH,qBAAqBI,OAAO;YAC9B,MAAMD,MAAMH;YACZG,IAAIE,IAAI,GAAG;YACX,MAAMF;QACR;QAEA,MAAM,EACJG,SAASC,WAAW,EACpBC,OAAOC,SAAS,EAChBC,QAAQC,UAAU,EACnB,GAAG,MAAMC,IAAAA,kBAAY,EAAChC,SAASkB,WAAWE,WAAW;YACpDV;YACAO;YACAT;YACAyB,SAAShC,gBAAgBE,UAAU,CAAC+B,IAAI,CAACjC;QAC3C;QAEA,MAAMkC,kBAAkBlC,gBACrBE,UAAU,CAAC,wBACXiC,OAAO,CAAC,IACPC,KAAKC,SAAS,CAAC;gBACbC,KAAKtB;gBACLa,QAAQV,UAAUU,MAAM;gBACxBF,OAAOR,UAAUQ,KAAK;gBACtBD;gBACAE;gBACAE;YACF;QAGJ,IAAIxB,iBAAiB,UAAU;YAC7B,IAAI,CAACiC,QAAQ,CAAC1B,kBAAkBd,SAAS;QAC3C,OAAO;YACL,IAAI,CAACwC,QAAQ,CACXC,aAAI,CAACC,IAAI,CACP,MACAlC,SAASD,iBAAiB,gBAAgB,KAAK,MAC/CO,mBAEFd,SACA;QAEJ;QAEA,OAAO,CAAC,eAAe,EAAEmC,gBAAgB,CAAC,CAAC;IAC7C;AACF;AACO,MAAMrC,MAAM;MACnB,WAAeC"}