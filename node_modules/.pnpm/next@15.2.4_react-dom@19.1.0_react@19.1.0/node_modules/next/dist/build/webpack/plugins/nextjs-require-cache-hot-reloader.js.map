{"version": 3, "sources": ["../../../../src/build/webpack/plugins/nextjs-require-cache-hot-reloader.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { deleteCache } from '../../../server/dev/require-cache'\nimport { clearModuleContext } from '../../../server/web/sandbox'\nimport path from 'path'\n\ntype Compiler = webpack.Compiler\ntype WebpackPluginInstance = webpack.WebpackPluginInstance\n\nconst RUNTIME_NAMES = ['webpack-runtime', 'webpack-api-runtime']\nconst PLUGIN_NAME = 'NextJsRequireCacheHotReloader'\n\n// This plugin flushes require.cache after emitting the files. Providing 'hot reloading' of server files.\nexport class NextJsRequireCacheHotReloader implements WebpackPluginInstance {\n  prevAssets: any = null\n  serverComponents: boolean\n\n  constructor(opts: { serverComponents: boolean }) {\n    this.serverComponents = opts.serverComponents\n  }\n\n  apply(compiler: Compiler) {\n    compiler.hooks.assetEmitted.tap(PLUGIN_NAME, (_file, { targetPath }) => {\n      // Clear module context in this process\n      clearModuleContext(targetPath)\n      deleteCache(targetPath)\n    })\n\n    compiler.hooks.afterEmit.tapPromise(PLUGIN_NAME, async (compilation) => {\n      for (const name of RUNTIME_NAMES) {\n        const runtimeChunkPath = path.join(\n          compilation.outputOptions.path!,\n          `${name}.js`\n        )\n        deleteCache(runtimeChunkPath)\n      }\n\n      // we need to make sure to clear all server entries from cache\n      // since they can have a stale webpack-runtime cache\n      // which needs to always be in-sync\n      const entries = [...compilation.entrypoints.keys()].filter((entry) => {\n        const isAppPath = entry.toString().startsWith('app/')\n        return entry.toString().startsWith('pages/') || isAppPath\n      })\n\n      for (const page of entries) {\n        const outputPath = path.join(\n          compilation.outputOptions.path!,\n          page + '.js'\n        )\n        deleteCache(outputPath)\n      }\n    })\n  }\n}\n"], "names": ["NextJsRequireCacheHotReloader", "RUNTIME_NAMES", "PLUGIN_NAME", "constructor", "opts", "prevAssets", "serverComponents", "apply", "compiler", "hooks", "assetEmitted", "tap", "_file", "targetPath", "clearModuleContext", "deleteCache", "afterEmit", "tapPromise", "compilation", "name", "runtimeChunk<PERSON><PERSON>", "path", "join", "outputOptions", "entries", "entrypoints", "keys", "filter", "entry", "isAppPath", "toString", "startsWith", "page", "outputPath"], "mappings": ";;;;+BAYaA;;;eAAAA;;;8BAXe;yBACO;6DAClB;;;;;;AAKjB,MAAMC,gBAAgB;IAAC;IAAmB;CAAsB;AAChE,MAAMC,cAAc;AAGb,MAAMF;IAIXG,YAAYC,IAAmC,CAAE;aAHjDC,aAAkB;QAIhB,IAAI,CAACC,gBAAgB,GAAGF,KAAKE,gBAAgB;IAC/C;IAEAC,MAAMC,QAAkB,EAAE;QACxBA,SAASC,KAAK,CAACC,YAAY,CAACC,GAAG,CAACT,aAAa,CAACU,OAAO,EAAEC,UAAU,EAAE;YACjE,uCAAuC;YACvCC,IAAAA,2BAAkB,EAACD;YACnBE,IAAAA,yBAAW,EAACF;QACd;QAEAL,SAASC,KAAK,CAACO,SAAS,CAACC,UAAU,CAACf,aAAa,OAAOgB;YACtD,KAAK,MAAMC,QAAQlB,cAAe;gBAChC,MAAMmB,mBAAmBC,aAAI,CAACC,IAAI,CAChCJ,YAAYK,aAAa,CAACF,IAAI,EAC9B,GAAGF,KAAK,GAAG,CAAC;gBAEdJ,IAAAA,yBAAW,EAACK;YACd;YAEA,8DAA8D;YAC9D,oDAAoD;YACpD,mCAAmC;YACnC,MAAMI,UAAU;mBAAIN,YAAYO,WAAW,CAACC,IAAI;aAAG,CAACC,MAAM,CAAC,CAACC;gBAC1D,MAAMC,YAAYD,MAAME,QAAQ,GAAGC,UAAU,CAAC;gBAC9C,OAAOH,MAAME,QAAQ,GAAGC,UAAU,CAAC,aAAaF;YAClD;YAEA,KAAK,MAAMG,QAAQR,QAAS;gBAC1B,MAAMS,aAAaZ,aAAI,CAACC,IAAI,CAC1BJ,YAAYK,aAAa,CAACF,IAAI,EAC9BW,OAAO;gBAETjB,IAAAA,yBAAW,EAACkB;YACd;QACF;IACF;AACF"}