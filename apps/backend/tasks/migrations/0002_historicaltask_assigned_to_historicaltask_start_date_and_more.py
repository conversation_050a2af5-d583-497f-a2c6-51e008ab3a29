# Generated by Django 4.2.9 on 2025-06-01 23:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("tasks", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicaltask",
            name="assigned_to",
            field=models.ForeignKey(
                blank=True,
                db_constraint=False,
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="+",
                to=settings.AUTH_USER_MODEL,
                verbose_name="مكلف إلى",
            ),
        ),
        migrations.AddField(
            model_name="historicaltask",
            name="start_date",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="تاريخ البداية"
            ),
        ),
        migrations.AddField(
            model_name="task",
            name="start_date",
            field=models.DateTimeField(
                blank=True, null=True, verbose_name="تاريخ البداية"
            ),
        ),
        migrations.AlterField(
            model_name="historicaltask",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "في الانتظار"),
                    ("in_progress", "قيد التنفيذ"),
                    ("review", "مراجعة"),
                    ("testing", "اختبار"),
                    ("completed", "مكتمل"),
                    ("cancelled", "ملغي"),
                ],
                default="pending",
                max_length=15,
                verbose_name="الحالة",
            ),
        ),
        migrations.RemoveField(
            model_name="task",
            name="assigned_to",
        ),
        migrations.AlterField(
            model_name="task",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "في الانتظار"),
                    ("in_progress", "قيد التنفيذ"),
                    ("review", "مراجعة"),
                    ("testing", "اختبار"),
                    ("completed", "مكتمل"),
                    ("cancelled", "ملغي"),
                ],
                default="pending",
                max_length=15,
                verbose_name="الحالة",
            ),
        ),
        migrations.AddField(
            model_name="task",
            name="assigned_to",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="assigned_tasks",
                to=settings.AUTH_USER_MODEL,
                verbose_name="مكلف إلى",
            ),
        ),
    ]
