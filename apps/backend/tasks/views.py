from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from .models import Task, TaskComment, TaskTimeLog
from .serializers import (
    TaskSerializer,
    TaskListSerializer,
    TaskCreateSerializer,
    TaskDetailSerializer,
    TaskCommentSerializer,
    TaskCommentCreateSerializer,
    TimeLogSerializer,
    TimeLogCreateSerializer,
    TaskStatsSerializer,
    TaskStatusUpdateSerializer
)


class TaskViewSet(viewsets.ModelViewSet):
    """Task management viewset"""
    queryset = Task.objects.all().select_related('project', 'assigned_to', 'created_by').order_by('-created_at')
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'priority', 'category', 'project', 'assigned_to']
    search_fields = ['title', 'description', 'project__name']
    ordering_fields = ['title', 'created_at', 'due_date', 'priority', 'estimated_hours']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return TaskListSerializer
        elif self.action == 'create':
            return TaskCreateSerializer
        elif self.action == 'retrieve':
            return TaskDetailSerializer
        return TaskSerializer

    def get_queryset(self):
        """Filter queryset based on user role"""
        queryset = super().get_queryset()
        user = self.request.user

        # Admins and sales managers can see all tasks
        if user.role in ['admin', 'sales_manager']:
            return queryset

        # Users can see tasks assigned to them or in projects they're involved in
        return queryset.filter(
            Q(assigned_to=user) |
            Q(project__assigned_team=user) |
            Q(project__project_manager=user) |
            Q(created_by=user)
        ).distinct()

    @action(detail=True, methods=['post'])
    def add_comment(self, request, pk=None):
        """Add comment to task"""
        task = self.get_object()
        serializer = TaskCommentCreateSerializer(
            data=request.data,
            context={'task': task, 'request': request}
        )

        if serializer.is_valid():
            comment = serializer.save()
            return Response({
                'comment': TaskCommentSerializer(comment).data,
                'message': 'تم إضافة التعليق بنجاح'
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def log_time(self, request, pk=None):
        """Log time for task"""
        task = self.get_object()
        serializer = TimeLogCreateSerializer(
            data=request.data,
            context={'task': task, 'request': request}
        )

        if serializer.is_valid():
            time_log = serializer.save()

            # Update task actual hours
            total_logged = task.time_logs.aggregate(total=Sum('hours'))['total'] or 0
            task.actual_hours = total_logged
            task.save(update_fields=['actual_hours'])

            return Response({
                'time_log': TimeLogSerializer(time_log).data,
                'message': f'تم تسجيل {time_log.hours} ساعة بنجاح'
            }, status=status.HTTP_201_CREATED)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """Update task status"""
        task = self.get_object()
        serializer = TaskStatusUpdateSerializer(data=request.data)

        if serializer.is_valid():
            new_status = serializer.validated_data['status']
            comment_text = serializer.validated_data.get('comment', '')

            old_status = task.status
            task.status = new_status

            # Set completion date if task is completed
            if new_status == 'completed' and old_status != 'completed':
                task.completed_at = timezone.now()
            elif new_status != 'completed':
                task.completed_at = None

            task.save(update_fields=['status', 'completed_at'])

            # Add status change comment if provided
            if comment_text:
                TaskComment.objects.create(
                    task=task,
                    user=request.user,
                    comment=f"تم تغيير الحالة من {task.get_status_display()} إلى {task.get_status_display()}: {comment_text}"
                )

            return Response({
                'task': TaskDetailSerializer(task).data,
                'message': f'تم تحديث حالة المهمة إلى {task.get_status_display()}'
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get task statistics"""
        queryset = self.get_queryset()

        # Basic counts
        total_tasks = queryset.count()
        pending_tasks = queryset.filter(status='pending').count()
        in_progress_tasks = queryset.filter(status='in_progress').count()
        completed_tasks = queryset.filter(status='completed').count()

        # Overdue tasks
        today = timezone.now().date()
        overdue_tasks = queryset.filter(
            due_date__lt=today,
            status__in=['pending', 'in_progress']
        ).count()

        # Time stats
        time_stats = queryset.aggregate(
            total_estimated=Sum('estimated_hours'),
            total_actual=Sum('actual_hours')
        )

        # Total logged hours from time logs
        total_logged = TaskTimeLog.objects.filter(
            task__in=queryset
        ).aggregate(total=Sum('hours'))['total'] or 0

        # Distribution stats
        status_distribution = dict(
            queryset.values('status').annotate(count=Count('id')).values_list('status', 'count')
        )

        priority_distribution = dict(
            queryset.values('priority').annotate(count=Count('id')).values_list('priority', 'count')
        )

        category_distribution = dict(
            queryset.values('category').annotate(count=Count('id')).values_list('category', 'count')
        )

        # Average completion time (for completed tasks)
        completed_tasks_with_dates = queryset.filter(
            status='completed',
            completed_at__isnull=False,
            created_at__isnull=False
        )

        avg_completion_time = 0
        if completed_tasks_with_dates.exists():
            completion_times = []
            for task in completed_tasks_with_dates:
                delta = task.completed_at - task.created_at
                completion_times.append(delta.total_seconds() / 3600)  # Convert to hours
            avg_completion_time = sum(completion_times) / len(completion_times)

        stats_data = {
            'total_tasks': total_tasks,
            'pending_tasks': pending_tasks,
            'in_progress_tasks': in_progress_tasks,
            'completed_tasks': completed_tasks,
            'overdue_tasks': overdue_tasks,
            'total_estimated_hours': time_stats['total_estimated'] or 0,
            'total_actual_hours': time_stats['total_actual'] or 0,
            'total_logged_hours': total_logged,
            'avg_completion_time': round(avg_completion_time, 2),
            'status_distribution': status_distribution,
            'priority_distribution': priority_distribution,
            'category_distribution': category_distribution
        }

        serializer = TaskStatsSerializer(stats_data)
        return Response(serializer.data)
