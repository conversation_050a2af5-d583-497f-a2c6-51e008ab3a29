from rest_framework import serializers
from .models import Task, TaskComment, TaskTimeLog
from projects.serializers import ProjectListSerializer
from authentication.serializers import UserListSerializer


class TaskSerializer(serializers.ModelSerializer):
    """Task serializer with all fields"""
    project = ProjectListSerializer(read_only=True)
    project_id = serializers.IntegerField(write_only=True)
    assigned_to = UserListSerializer(read_only=True)
    assigned_to_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    created_by = UserListSerializer(read_only=True)
    
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    
    total_time_logged = serializers.SerializerMethodField()
    comments_count = serializers.IntegerField(source='comments.count', read_only=True)
    
    class Meta:
        model = Task
        fields = [
            'id', 'title', 'description', 'status', 'status_display',
            'priority', 'priority_display', 'category', 'category_display',
            'project', 'project_id', 'assigned_to', 'assigned_to_id',
            'created_by', 'estimated_hours', 'actual_hours',
            'start_date', 'due_date', 'completed_at',
            'total_time_logged', 'comments_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by', 'completed_at', 'created_at', 'updated_at']
    
    def get_total_time_logged(self, obj):
        """Calculate total time logged for this task"""
        return obj.time_logs.aggregate(
            total=serializers.models.Sum('hours')
        )['total'] or 0
    
    def create(self, validated_data):
        """Create task with current user as creator"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class TaskListSerializer(serializers.ModelSerializer):
    """Simplified task serializer for lists"""
    project_name = serializers.CharField(source='project.name', read_only=True)
    assigned_to_name = serializers.CharField(source='assigned_to.full_name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    
    class Meta:
        model = Task
        fields = [
            'id', 'title', 'status', 'status_display',
            'priority', 'priority_display', 'category', 'category_display',
            'project_name', 'assigned_to_name',
            'estimated_hours', 'actual_hours',
            'start_date', 'due_date', 'created_at'
        ]


class TaskCreateSerializer(serializers.ModelSerializer):
    """Task creation serializer"""
    
    class Meta:
        model = Task
        fields = [
            'title', 'description', 'status', 'priority', 'category',
            'project_id', 'assigned_to_id', 'estimated_hours',
            'start_date', 'due_date'
        ]
    
    def create(self, validated_data):
        """Create task with current user as creator"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class TaskCommentSerializer(serializers.ModelSerializer):
    """Task comment serializer"""
    user = UserListSerializer(read_only=True)

    class Meta:
        model = TaskComment
        fields = ['id', 'task', 'user', 'content', 'created_at', 'updated_at']
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']

    def create(self, validated_data):
        """Create comment with current user"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class TaskCommentCreateSerializer(serializers.ModelSerializer):
    """Task comment creation serializer"""

    class Meta:
        model = TaskComment
        fields = ['content']

    def create(self, validated_data):
        """Create comment with task and user from context"""
        validated_data['task'] = self.context['task']
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class TimeLogSerializer(serializers.ModelSerializer):
    """Time log serializer"""
    user = UserListSerializer(read_only=True)

    class Meta:
        model = TaskTimeLog
        fields = [
            'id', 'task', 'user', 'hours', 'description',
            'date', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']

    def create(self, validated_data):
        """Create time log with current user"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class TimeLogCreateSerializer(serializers.ModelSerializer):
    """Time log creation serializer"""

    class Meta:
        model = TaskTimeLog
        fields = ['hours', 'description', 'date']

    def create(self, validated_data):
        """Create time log with task and user from context"""
        validated_data['task'] = self.context['task']
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)


class TaskDetailSerializer(serializers.ModelSerializer):
    """Detailed task serializer with related data"""
    project = ProjectListSerializer(read_only=True)
    assigned_to = UserListSerializer(read_only=True)
    created_by = UserListSerializer(read_only=True)
    
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    priority_display = serializers.CharField(source='get_priority_display', read_only=True)
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    
    comments = TaskCommentSerializer(many=True, read_only=True)
    time_logs = TimeLogSerializer(many=True, read_only=True)
    total_time_logged = serializers.SerializerMethodField()
    
    class Meta:
        model = Task
        fields = [
            'id', 'title', 'description', 'status', 'status_display',
            'priority', 'priority_display', 'category', 'category_display',
            'project', 'assigned_to', 'created_by',
            'estimated_hours', 'actual_hours', 'total_time_logged',
            'start_date', 'due_date', 'completed_at',
            'comments', 'time_logs',
            'created_at', 'updated_at'
        ]
    
    def get_total_time_logged(self, obj):
        """Calculate total time logged for this task"""
        return obj.time_logs.aggregate(
            total=serializers.models.Sum('hours')
        )['total'] or 0


class TaskStatsSerializer(serializers.Serializer):
    """Task statistics serializer"""
    total_tasks = serializers.IntegerField()
    pending_tasks = serializers.IntegerField()
    in_progress_tasks = serializers.IntegerField()
    completed_tasks = serializers.IntegerField()
    overdue_tasks = serializers.IntegerField()
    total_estimated_hours = serializers.FloatField()
    total_actual_hours = serializers.FloatField()
    total_logged_hours = serializers.FloatField()
    avg_completion_time = serializers.FloatField()
    status_distribution = serializers.DictField()
    priority_distribution = serializers.DictField()
    category_distribution = serializers.DictField()


class TaskStatusUpdateSerializer(serializers.Serializer):
    """Task status update serializer"""
    status = serializers.ChoiceField(choices=Task.Status.choices)
    comment = serializers.CharField(required=False, allow_blank=True)
