'use client';

import { useEffect } from 'react';
import { useAuthStore } from '@/lib/stores/auth-store';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useAuthStore();

  useEffect(() => {
    // Hydrate the store on client side
    useAuthStore.persist.rehydrate();
    
    // Initialize auth if token exists
    const token = localStorage.getItem('access_token');
    if (token && !isAuthenticated) {
      useAuthStore.getState().getProfile();
    }
  }, [isAuthenticated]);

  return <>{children}</>;
}
